<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en_US" sourcelanguage="zh_CN">
<context>
    <name>cls.obj</name>
    <message>
        <location filename="../src/tools/str.py" line="266"/>
        <source>成功</source>
        <translation>Success</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="267"/>
        <source>加载</source>
        <translation>Loading</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="268"/>
        <source>错误</source>
        <translation>Error</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="269"/>
        <source>等待</source>
        <translation>Waiting</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="270"/>
        <source>网络错误</source>
        <translation>Network Error</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="271"/>
        <source>用户名密码错误</source>
        <translation>Username or password incorrect</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="272"/>
        <source>注册失败</source>
        <translation>Registration failed</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="273"/>
        <source>未知错误</source>
        <translation>Unknown error</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="274"/>
        <source>未找到书籍</source>
        <translation>Book not found</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="275"/>
        <source>解析出错了</source>
        <translation>Parsing error</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="276"/>
        <source>需要谷歌验证</source>
        <translation>Google verification needed</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="277"/>
        <source>头像设置出错了, 请尽量选择500kb以下的图片</source>
        <translation>Error setting avatar, please try to choose an image less than 500kb</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="278"/>
        <source>本子审核中</source>
        <translation>Manga under review</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="279"/>
        <source>未登录</source>
        <translation>Not logged in</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="280"/>
        <source>保存出错</source>
        <translation>Error saving</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="281"/>
        <source>缓存</source>
        <translation>Cache</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="282"/>
        <source>Add错误</source>
        <translation>Add error</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="283"/>
        <source>路径错误</source>
        <translation>Path error</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="284"/>
        <source>未发现源文件</source>
        <translation>Source file not found</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="285"/>
        <source>文件损坏</source>
        <translation>File corrupted</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="286"/>
        <source>连接超时</source>
        <translation>Connection timeout</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="287"/>
        <source>无法连接</source>
        <translation>Unable to connect</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="288"/>
        <source>证书错误</source>
        <translation>Certificate error</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="289"/>
        <source>连接被重置</source>
        <translation>Connection reset</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="290"/>
        <source>无法连接代理</source>
        <translation>Cannot connect to proxy</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="291"/>
        <source>下载失败</source>
        <translation>Download failed</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="292"/>
        <source>离线模式中</source>
        <translation>In offline mode</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="293"/>
        <source>未下载</source>
        <translation>Not downloaded</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="294"/>
        <source>账号已被注册</source>
        <translation>Account already registered</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="295"/>
        <source>未成年禁止注册</source>
        <translation>Registration prohibited for minors</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="297"/>
        <location filename="../src/tools/str.py" line="335"/>
        <source>空白章节</source>
        <translation>Blank chapter</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="298"/>
        <source>图片加载中...</source>
        <translation>Image loading...</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="299"/>
        <source>图片加载失败</source>
        <translation>Image loading failed</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="300"/>
        <source>使用Cookie登录</source>
        <translation>Login with Cookie</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="301"/>
        <source>使用账号登录</source>
        <translation>Login with account</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="302"/>
        <source>不能为空</source>
        <translation>Cannot be empty</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="303"/>
        <source>登录失败</source>
        <translation>Login failed</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="304"/>
        <location filename="../src/tools/str.py" line="316"/>
        <source>下载完成</source>
        <translation>Download complete</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="305"/>
        <source>获取信息</source>
        <translation>Fetching information</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="306"/>
        <source>获取分页</source>
        <translation>Fetching pagination</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="307"/>
        <source>获取下载地址</source>
        <translation>Fetching download address</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="308"/>
        <source>正在下载封面</source>
        <translation>Downloading cover</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="309"/>
        <source>正在下载</source>
        <translation>Downloading</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="310"/>
        <location filename="../src/tools/str.py" line="319"/>
        <source>等待中</source>
        <translation>Waiting</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="311"/>
        <location filename="../src/tools/str.py" line="343"/>
        <source>暂停</source>
        <translation>Paused</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="312"/>
        <location filename="../src/tools/str.py" line="342"/>
        <source>出错了</source>
        <translation>Error occurred</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="313"/>
        <source>原始文件不存在</source>
        <translation>Original file does not exist</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="314"/>
        <source>转换中</source>
        <translation>Converting</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="315"/>
        <source>转换成功</source>
        <translation>Conversion successful</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="317"/>
        <source>下载错误</source>
        <translation>Download error</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="318"/>
        <source>重新下载</source>
        <translation>Redownload</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="320"/>
        <source>转换开始</source>
        <translation>Conversion started</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="321"/>
        <source>不转换</source>
        <translation>Do not convert</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="322"/>
        <source>转换完成</source>
        <translation>Conversion complete</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="323"/>
        <source>转换失败</source>
        <translation>Conversion failed</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="324"/>
        <source>超过设置分辨率</source>
        <translation>Exceeds set resolution</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="326"/>
        <source>动图不自动转换</source>
        <translation>GIFs do not convert automatically</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="327"/>
        <source>未找到该章节</source>
        <translation>Chapter not found</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="328"/>
        <source>超过索引</source>
        <translation>Index out of bounds</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="329"/>
        <source>未找到该图片</source>
        <translation>Image not found</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="330"/>
        <source>不是ZIP文件</source>
        <translation>Not a ZIP file</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="331"/>
        <source>错误的路径</source>
        <translation>Incorrect path</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="332"/>
        <source>没有发现图片文件</source>
        <translation>No image files found</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="333"/>
        <source>文件已加密</source>
        <translation>File is encrypted</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="336"/>
        <source>网络存储</source>
        <translation>Network storage</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="337"/>
        <source>完成</source>
        <translation>Completed</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="338"/>
        <source>等待下载</source>
        <translation>Waiting for download</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="339"/>
        <source>等待Waifu2x</source>
        <translation>Waiting for Waifu2x</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="340"/>
        <source>正在打包</source>
        <translation>Packaging</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="341"/>
        <source>正在上传</source>
        <translation>Uploading</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="344"/>
        <source>连接中</source>
        <translation>Connecting</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="345"/>
        <source>没有网络存储</source>
        <translation>No network storage</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="346"/>
        <source>空</source>
        <translation>Empty</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="347"/>
        <source>打包出错了</source>
        <translation>Packaging error</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="348"/>
        <source>验证失败</source>
        <translation>Verification failed</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="349"/>
        <source>本地文件未找到</source>
        <translation>Local file not found</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="350"/>
        <source>空间不足</source>
        <translation>Insufficient space</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="351"/>
        <source>连接失败</source>
        <translation>Connection failed</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="352"/>
        <source>需重新设置存储</source>
        <translation>Storage needs to be reset</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="353"/>
        <source>未找到网络存储</source>
        <translation>Network storage not found</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="354"/>
        <source>不支持该方法</source>
        <translation>Method not supported</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="355"/>
        <source>创建目录失败</source>
        <translation>Failed to create directory</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="357"/>
        <source>添加上传成功</source>
        <translation>Upload added successfully</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="358"/>
        <source>菜单</source>
        <translation>Menu</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="359"/>
        <source>全屏切换</source>
        <translation>Toggle Fullscreen</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="360"/>
        <source>阅读模式</source>
        <translation>Reading Mode</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="361"/>
        <source>上下滚动</source>
        <translation>Scroll Vertically</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="362"/>
        <source>默认</source>
        <translation>Default</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="363"/>
        <source>左右双页</source>
        <translation>Left-Right Double Page</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="364"/>
        <source>右左双页</source>
        <translation>Right-Left Double Page</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="365"/>
        <source>左右滚动</source>
        <translation>Scroll Left-Right</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="366"/>
        <source>右左滚动</source>
        <translation>Scroll Right-Left</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="367"/>
        <source>缩放</source>
        <translation>Zoom</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="368"/>
        <source>切页</source>
        <translation>Page Turning</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="369"/>
        <source>上一章</source>
        <translation>Previous Chapter</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="370"/>
        <source>下一章</source>
        <translation>Next Chapter</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="371"/>
        <source>退出</source>
        <translation>Exit</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="372"/>
        <source>自动滚动/翻页</source>
        <translation>Auto Scroll/Page Turn</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="373"/>
        <source>退出全屏</source>
        <translation>Exit Fullscreen</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="374"/>
        <source>全屏</source>
        <translation>Fullscreen</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="375"/>
        <source>继续阅读</source>
        <translation>Continue Reading</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="376"/>
        <source>页</source>
        <translation>Page</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="377"/>
        <source>已经是第一页</source>
        <translation>Already on the first page</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="378"/>
        <source>已经最后一页</source>
        <translation>Already on the last page</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="379"/>
        <source>自动跳转到上一章</source>
        <translation>Automatically jump to the previous chapter</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="380"/>
        <source>自动跳转到下一章</source>
        <translation>Automatically jump to the next chapter</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="381"/>
        <source>位置</source>
        <translation>Location</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="382"/>
        <source>分辨率</source>
        <translation>Resolution</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="383"/>
        <source>大小</source>
        <translation>Size</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="384"/>
        <source>状态</source>
        <translation>Status</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="385"/>
        <source>下载未完成</source>
        <translation>Download incomplete</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="386"/>
        <source>Waifu2x当前为CPU模式，看图模式下不推荐开启</source>
        <translation>Waifu2x is currently in CPU mode, not recommended to enable in viewing mode</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="387"/>
        <source>自动滚动/翻页已停止</source>
        <translation>Auto scroll/page turn has stopped</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="388"/>
        <source>上一页</source>
        <translation>Previous page</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="389"/>
        <source>下一页</source>
        <translation>Next page</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="390"/>
        <source>上滑</source>
        <translation>Swipe up</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="392"/>
        <source>下滑</source>
        <translation>Swipe down</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="393"/>
        <source>无代理</source>
        <translation>No proxy</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="394"/>
        <source>保存成功</source>
        <translation>Save successful</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="395"/>
        <source>登录</source>
        <translation>Login</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="396"/>
        <source>注册</source>
        <translation>Register</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="397"/>
        <source>测速</source>
        <translation>Speed test</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="398"/>
        <source>密码太短</source>
        <translation>Password too short</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="399"/>
        <source>注册成功</source>
        <translation>Registration successful</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="400"/>
        <source>完结</source>
        <translation>Finished</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="401"/>
        <source>选择文件夹</source>
        <translation>Select folder</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="402"/>
        <source>保存</source>
        <translation>Save</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="403"/>
        <source>评论加载失败</source>
        <translation>Failed to load comments</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="404"/>
        <source>置顶</source>
        <translation>Pin to top</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="405"/>
        <source>第</source>
        <translation>Number</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="406"/>
        <source>楼</source>
        <translation>Floor</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="407"/>
        <source>天前</source>
        <translation>days ago</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="408"/>
        <source>小时前</source>
        <translation>hours ago</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="409"/>
        <source>分钟前</source>
        <translation>minutes ago</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="410"/>
        <source>秒前</source>
        <translation>seconds ago</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="411"/>
        <source>收藏数</source>
        <translation>Favorites</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="412"/>
        <source>正在加载收藏分页</source>
        <translation>Loading favorites pagination</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="413"/>
        <source>更新完成</source>
        <translation>Update completed</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="414"/>
        <source>图片</source>
        <translation>Image</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="415"/>
        <source>正在发送</source>
        <translation>Sending</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="416"/>
        <source>在线人数</source>
        <translation>Online users</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="417"/>
        <source>已经是第一章</source>
        <translation>Already the first chapter</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="418"/>
        <source>已经最后一章</source>
        <translation>Already the last chapter</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="419"/>
        <source>章节加载失败</source>
        <translation>Chapter loading failed</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="420"/>
        <source>添加收藏成功</source>
        <translation>Successfully added to favorites</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="421"/>
        <source>转换</source>
        <translation>Convert</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="422"/>
        <source>复制成功</source>
        <translation>Copy successful</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="423"/>
        <source>头像上传中......</source>
        <translation>Uploading avatar......</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="424"/>
        <source>更新</source>
        <translation>Update</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="425"/>
        <source>已打卡</source>
        <translation>Checked in</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="426"/>
        <source>打卡</source>
        <translation>Check in</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="427"/>
        <source>屏蔽</source>
        <translation>Block</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="428"/>
        <source>取消屏蔽</source>
        <translation>Unblock</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="429"/>
        <source>打开目录</source>
        <translation>Open directory</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="430"/>
        <source>删除记录</source>
        <translation>Delete record</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="431"/>
        <source>删除记录和文件 </source>
        <translation>Delete records and files</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="432"/>
        <source>选择下载章节</source>
        <translation>Select download chapter</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="433"/>
        <source>开始</source>
        <translation>Start</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="434"/>
        <source>开始转换</source>
        <translation>Start conversion</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="435"/>
        <source>暂停转换</source>
        <translation>Pause conversion</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="436"/>
        <source>打开</source>
        <translation>Open</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="437"/>
        <source>查看封面</source>
        <translation>View cover</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="438"/>
        <source>重下封面</source>
        <translation>Redownload cover</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="439"/>
        <source>Waifu2x转换</source>
        <translation>Waifu2x conversion</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="440"/>
        <source>复制标题</source>
        <translation>Copy title</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="441"/>
        <source>下载</source>
        <translation>Download</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="442"/>
        <source>删除</source>
        <translation>Delete</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="443"/>
        <source>当前版本</source>
        <translation>Current version</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="444"/>
        <source>检查到更新，是否前往更新</source>
        <translation>Update available, proceed to update?</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="445"/>
        <source>复制Android下载地址</source>
        <translation>Copy Android download address</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="446"/>
        <source>复制IOS下载地址</source>
        <translation>Copy iOS download address</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="447"/>
        <source>请设置目录</source>
        <translation>Please set the directory</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="448"/>
        <source>添加下载成功</source>
        <translation>Successfully added download</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="449"/>
        <source>观看第1章</source>
        <translation>Watch Chapter 1</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="450"/>
        <source>上次看到第</source>
        <translation>Last seen at chapter</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="451"/>
        <source>章</source>
        <translation>Chapter</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="452"/>
        <source>看过</source>
        <translation>Watched</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="453"/>
        <source>按Enter发送消息</source>
        <translation>Press Enter to send message</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="454"/>
        <source>按Ctrl+Enter发送消息</source>
        <translation>Press Ctrl+Enter to send message</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="455"/>
        <source>取消Waifu2x转换</source>
        <translation>Cancel Waifu2x conversion</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="456"/>
        <source>需要重启保存</source>
        <translation>Need to restart to save</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="457"/>
        <source>检查更新</source>
        <translation>Check for updates</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="458"/>
        <source>今日已更新</source>
        <translation>Updated today</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="459"/>
        <source>有更新</source>
        <translation>There is an update</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="460"/>
        <source>已是最新</source>
        <translation>Already up to date</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="461"/>
        <source>最近更新</source>
        <translation>Recently updated</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="462"/>
        <source>留言板</source>
        <translation>Message board</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="463"/>
        <source>排行版</source>
        <translation>Ranking</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="464"/>
        <source>随机本子</source>
        <translation>Random manga</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="465"/>
        <source>删除成功</source>
        <translation>Deleted successfully</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="466"/>
        <source>所有</source>
        <translation>All</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="467"/>
        <source>收藏</source>
        <translation>Favorites</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="468"/>
        <source>分类</source>
        <translation>Category</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="469"/>
        <source>评论</source>
        <translation>Comments</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="470"/>
        <source>更改</source>
        <translation>Change</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="471"/>
        <source>表里切换</source>
        <translation>Toggle inside and out</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="472"/>
        <source>删除收藏成功</source>
        <translation>Successfully removed from favorites</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="473"/>
        <source>所有评论</source>
        <translation>All comments</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="474"/>
        <source>移动</source>
        <translation>Move</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="475"/>
        <source>新增</source>
        <translation>Add</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="476"/>
        <source>全选</source>
        <translation>Select All</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="477"/>
        <source>反选</source>
        <translation>Invert Selection</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="478"/>
        <source>我的评论</source>
        <translation>My comments</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="479"/>
        <source>登出</source>
        <translation>Log out</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="480"/>
        <source>Sock5设置出错</source>
        <translation>Sock5 settings error</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="481"/>
        <source>开启自动waifu2x</source>
        <translation>Enable auto waifu2x</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="482"/>
        <source>关闭自动waifu2x</source>
        <translation>Disable auto waifu2x</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="483"/>
        <source>开启本张图waifu2x</source>
        <translation>Enable waifu2x for this image</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="484"/>
        <source>关闭本张图waifu2x</source>
        <translation>Disable waifu2x for this image</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="485"/>
        <source>右左双页(滚轮正序)</source>
        <translation>Right to left double page (wheel forward)</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="486"/>
        <source>复制</source>
        <translation>Copy</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="487"/>
        <source>复制图片到剪贴板</source>
        <translation>Copy image to clipboard</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="488"/>
        <source>保存文件</source>
        <translation>Save file</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="489"/>
        <source>主界面</source>
        <translation>Main interface</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="490"/>
        <source>最小化</source>
        <translation>Minimize</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="491"/>
        <source>批量下载</source>
        <translation>Batch download</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="492"/>
        <source>导入单本目录</source>
        <translation>Import a single directory</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="493"/>
        <source>导入单本Zip</source>
        <translation>Import a single Zip</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="494"/>
        <source>批量导入单本目录</source>
        <translation>Batch import single directories</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="495"/>
        <source>批量导入带章节目录</source>
        <translation>Batch import directories with chapters</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="496"/>
        <source>支持拖拽文件导入</source>
        <translation>Support for drag and drop file import</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="497"/>
        <source>已存在</source>
        <translation>Already exists</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="498"/>
        <source>等宽模式</source>
        <translation>Equal width mode</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="499"/>
        <source>保存上次路径</source>
        <translation>Save last path</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="500"/>
        <source>导入多章节目录</source>
        <translation>Import multi-chapter directories</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="501"/>
        <source>导入到本地漫画中</source>
        <translation>Import into local comics</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="502"/>
        <source>没有可更新章节</source>
        <translation>No chapters to update</translation>
    </message>
    <message>
        <location filename="../src/tools/str.py" line="504"/>
        <source>正在看</source>
        <translation>Currently watching</translation>
    </message>
</context>
</TS>
