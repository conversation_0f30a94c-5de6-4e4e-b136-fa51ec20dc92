#!/usr/bin/env python3
"""
示例：如何使用新的专辑下载功能

注意：这个示例需要在完整的Qt环境中运行，这里仅作为代码参考
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def example_album_download():
    """
    演示如何使用专辑下载功能
    """
    print("专辑下载功能示例")
    print("=" * 40)
    
    try:
        from task.qt_task import QtTaskBase
        from view.download.download_item import DownloadItem
        
        # 创建下载任务基类实例
        task_base = QtTaskBase()
        
        # 创建下载项目
        download_item = DownloadItem()
        download_item.bookId = "123456"  # 示例书籍ID
        download_item.title = "示例专辑"
        download_item.author = "示例作者"
        
        # 定义回调函数
        def status_callback(data, task_id):
            """状态回调"""
            status = data.get("st", "Unknown")
            print(f"状态更新 [任务{task_id}]: {status}")
            
        def progress_callback(downloaded_size, remaining_size, task_id):
            """进度回调"""
            total_size = downloaded_size + remaining_size
            if total_size > 0:
                progress = (downloaded_size / total_size) * 100
                print(f"下载进度 [任务{task_id}]: {progress:.1f}% ({downloaded_size}/{total_size})")
            
        def complete_callback(data, status, task_id):
            """完成回调"""
            print(f"下载完成 [任务{task_id}]: 状态={status}")
            
        # 获取保存路径
        save_path = download_item.GetAlbumSavePath()
        print(f"保存路径: {save_path}")
        
        # 方法1: 使用QtTaskBase的专辑下载方法
        print("\n方法1: 使用QtTaskBase.AddDownloadAlbum")
        if hasattr(task_base, 'AddDownloadAlbum'):
            print("✓ AddDownloadAlbum方法可用")
        else:
            print("✗ AddDownloadAlbum方法不可用")
            
        # 方法2: 直接使用TaskDownload
        print("\n方法2: 直接使用TaskDownload.DownloadAlbum")
        from task.task_download import TaskDownload
        
        downloader = TaskDownload()
        if hasattr(downloader, 'DownloadAlbum'):
            print("✓ DownloadAlbum方法可用")
        else:
            print("✗ DownloadAlbum方法不可用")
            
        print("\n在实际应用中的使用方式：")
        print("""
        # 通过DownloadStatus（推荐方式）
        from view.download.download_status import DownloadStatus
        
        download_status = DownloadStatus()
        download_item = DownloadItem()
        download_item.bookId = "123456"
        download_item.title = "专辑标题"
        
        # 系统会自动判断是否使用专辑下载
        download_status.StartItemDownload(download_item)
        """)
        
    except Exception as e:
        print(f"示例执行出错: {e}")

def main():
    """主函数"""
    print("JMComic-qt 专辑下载功能示例")
    print("=" * 50)
    
    # 使用示例
    example_album_download()
    
    print("\n" + "=" * 50)
    print("示例完成！")

if __name__ == "__main__":
    main()
