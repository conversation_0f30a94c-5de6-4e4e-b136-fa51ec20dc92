#!/usr/bin/env python3
"""
测试专辑下载修复是否有效
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'JMComic-qt', 'src'))

def test_type_safety():
    """测试类型安全性"""
    print("测试类型安全性")
    print("=" * 40)
    
    try:
        from task.task_download import TaskDownload
        
        downloader = TaskDownload()
        
        # 测试1: 正常的字符串bookId
        print("测试1: 正常字符串bookId")
        task_id = downloader.DownloadAlbum("123456")
        if task_id:
            print("✓ 正常字符串bookId测试通过")
        else:
            print("✗ 正常字符串bookId测试失败")
        
        # 测试2: 数字bookId
        print("\n测试2: 数字bookId")
        task_id = downloader.DownloadAlbum(123456)
        if task_id:
            print("✓ 数字bookId测试通过")
        else:
            print("✗ 数字bookId测试失败")
        
        # 测试3: 空bookId
        print("\n测试3: 空bookId")
        task_id = downloader.DownloadAlbum("")
        if task_id is None:
            print("✓ 空bookId正确拒绝")
        else:
            print("✗ 空bookId应该被拒绝")
        
        # 测试4: None bookId
        print("\n测试4: None bookId")
        task_id = downloader.DownloadAlbum(None)
        if task_id is None:
            print("✓ None bookId正确拒绝")
        else:
            print("✗ None bookId应该被拒绝")
        
        # 测试5: 字典类型的savePath（模拟可能导致rfind错误的情况）
        print("\n测试5: 非字符串savePath")
        task_id = downloader.DownloadAlbum("123456", savePath={"path": "/test"})
        if task_id:
            print("✓ 非字符串savePath测试通过")
        else:
            print("✗ 非字符串savePath测试失败")
            
        return True
        
    except Exception as e:
        print(f"✗ 类型安全性测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_jmcomic_option_creation():
    """测试jmcomic选项创建"""
    print("\n测试jmcomic选项创建")
    print("=" * 40)
    
    try:
        from task.task_download import TaskDownload, QtDownloadTask
        
        downloader = TaskDownload()
        
        # 创建测试任务
        task = QtDownloadTask()
        task.bookId = "123456"
        task.savePath = "/test/path"
        
        # 测试选项创建
        option = downloader._CreateJmcomicOption(task)
        print(f"✓ 选项创建成功: {type(option)}")
        
        # 测试空路径
        task.savePath = None
        option = downloader._CreateJmcomicOption(task)
        print("✓ 空路径处理成功")
        
        # 测试字典类型路径（可能导致问题的情况）
        task.savePath = {"path": "/test"}
        option = downloader._CreateJmcomicOption(task)
        print("✓ 字典路径转换成功")
        
        return True
        
    except Exception as e:
        print(f"✗ jmcomic选项创建测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n测试错误处理")
    print("=" * 40)
    
    try:
        from task.task_download import TaskDownload
        
        # 测试jmcomic不可用的情况
        original_available = TaskDownload.__module__
        
        # 模拟jmcomic不可用
        import task.task_download as td
        original_jmcomic_available = td.JMCOMIC_AVAILABLE
        td.JMCOMIC_AVAILABLE = False
        
        downloader = TaskDownload()
        task_id = downloader.DownloadAlbum("123456")
        
        if task_id is None:
            print("✓ jmcomic不可用时正确处理")
        else:
            print("✗ jmcomic不可用时应该返回None")
        
        # 恢复原始状态
        td.JMCOMIC_AVAILABLE = original_jmcomic_available
        
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False

def test_syntax_check():
    """语法检查"""
    print("\n语法检查")
    print("=" * 40)
    
    files_to_check = [
        "JMComic-qt/src/task/task_download.py",
        "JMComic-qt/src/task/qt_task.py",
        "JMComic-qt/src/view/download/download_status.py",
        "JMComic-qt/src/view/download/download_item.py"
    ]
    
    all_passed = True
    for file_path in files_to_check:
        try:
            import py_compile
            py_compile.compile(file_path, doraise=True)
            print(f"✓ {os.path.basename(file_path)}: 语法正确")
        except Exception as e:
            print(f"✗ {os.path.basename(file_path)}: 语法错误 - {e}")
            all_passed = False
    
    return all_passed

def main():
    """主函数"""
    print("JMComic-qt 专辑下载修复测试")
    print("=" * 50)
    
    tests = [
        ("语法检查", test_syntax_check),
        ("类型安全性", test_type_safety),
        ("jmcomic选项创建", test_jmcomic_option_creation),
        ("错误处理", test_error_handling),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("测试总结:")
    passed = 0
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n通过: {passed}/{len(results)} 测试")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！rfind错误应该已修复。")
    else:
        print(f"\n⚠️  {len(results) - passed} 个测试失败，请检查实现。")

if __name__ == "__main__":
    main()
