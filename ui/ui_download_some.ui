<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DownloadSome</class>
 <widget class="QWidget" name="DownloadSome">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>755</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="totalLabel">
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="inputButton">
       <property name="text">
        <string>批量输入JM号</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="loadInfoButton">
       <property name="text">
        <string>获取信息</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="cleanButton">
       <property name="text">
        <string>清空</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="nasButton">
       <property name="text">
        <string>上传</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="downButton">
       <property name="text">
        <string>下载</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTableWidget" name="tableWidget">
     <column>
      <property name="text">
       <string>JM号</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>标题</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>章节数</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>状态</string>
      </property>
     </column>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
