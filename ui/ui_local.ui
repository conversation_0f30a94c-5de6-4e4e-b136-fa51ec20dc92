<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Local</class>
 <widget class="QWidget" name="Local">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>626</width>
    <height>327</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>本地漫画</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="3" column="0">
    <layout class="QGridLayout" name="gridLayout_3">
     <item row="0" column="0">
      <widget class="ComicListWidget" name="bookList">
       <property name="styleSheet">
        <string notr="true"/>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="TagListWidget" name="tagsList">
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>60</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">QListWidget {background-color:transparent;}
QListWidget::item {
    background-color:rgb(251, 239, 243);
    color: rgb(196, 95, 125);
	border:2px solid red;
    border-radius: 10px;
	border-color:rgb(196, 95, 125);
}
/* 鼠标在按钮上时，按钮颜色 */
 QListWidget::item:hover 
{
    background-color:rgb(21, 85, 154);
    border-radius: 10px;
    color: rgb(0, 0, 0);
}
 QListWidget::item:selected 
{
    background-color:rgb(21, 85, 154);
    border-radius: 10px;
    color: rgb(0, 0, 0);
}</string>
       </property>
       <property name="frameShape">
        <enum>QFrame::NoFrame</enum>
       </property>
       <property name="spacing">
        <number>6</number>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="delAll">
       <property name="text">
        <string>批量删除</string>
       </property>
      </widget>
     </item>
     <item alignment="Qt::AlignRight">
      <widget class="QPushButton" name="toolButton">
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>20</height>
        </size>
       </property>
       <property name="text">
        <string>导入</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="4" column="0">
    <layout class="QGridLayout" name="gridLayout_4">
     <item row="0" column="0">
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QLabel" name="msgLabel">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="sortKeyCombox">
         <property name="enabled">
          <bool>true</bool>
         </property>
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>0</height>
          </size>
         </property>
         <item>
          <property name="text">
           <string>上次阅读时间</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>添加时间</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>名称</string>
          </property>
         </item>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="sortIdCombox">
         <property name="enabled">
          <bool>true</bool>
         </property>
         <item>
          <property name="text">
           <string>降序</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>升序</string>
          </property>
         </item>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="Line" name="line_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="nums">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="text">
          <string>收藏数：</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="pages">
         <property name="text">
          <string>页</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="Line" name="line">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="Line" name="line_4">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QSpinBox" name="spinBox">
         <property name="minimumSize">
          <size>
           <width>50</width>
           <height>30</height>
          </size>
         </property>
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>1</number>
         </property>
        </widget>
       </item>
       <item>
        <widget class="Line" name="line_3">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="jumpButton">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="text">
          <string>跳转</string>
         </property>
         <property name="shortcut">
          <string>Return</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item row="0" column="0">
    <widget class="QWidget" name="widget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <item>
       <widget class="QLabel" name="label">
        <property name="text">
         <string>搜索：</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="lineEdit"/>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ComicListWidget</class>
   <extends>QListWidget</extends>
   <header location="global">component.list.comic_list_widget.h</header>
  </customwidget>
  <customwidget>
   <class>TagListWidget</class>
   <extends>QListWidget</extends>
   <header location="global">component.list.tag_list_widget.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>jumpButton</sender>
   <signal>clicked()</signal>
   <receiver>Local</receiver>
   <slot>JumpPage()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>439</x>
     <y>280</y>
    </hint>
    <hint type="destinationlabel">
     <x>361</x>
     <y>291</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>JumpPage()</slot>
  <slot>RefreshDataFocus()</slot>
 </slots>
</ui>
