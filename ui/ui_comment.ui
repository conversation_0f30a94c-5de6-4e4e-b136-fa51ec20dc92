<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Comment</class>
 <widget class="QWidget" name="Comment">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>评论</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QWidget" name="selectWidget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <item>
       <widget class="QComboBox" name="selectBox">
        <item>
         <property name="text">
          <string>所有</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>漫画</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>闲聊</string>
         </property>
        </item>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="UserListWidget" name="listWidget"/>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLineEdit" name="commentLine"/>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton">
       <property name="text">
        <string>回复</string>
       </property>
       <property name="shortcut">
        <string>Return</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="nums">
       <property name="text">
        <string>TextLabel</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QSpinBox" name="spinBox"/>
     </item>
     <item>
      <widget class="QPushButton" name="skipButton">
       <property name="text">
        <string>跳转</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>UserListWidget</class>
   <extends>QListWidget</extends>
   <header location="global">component.list.user_list_widget.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
