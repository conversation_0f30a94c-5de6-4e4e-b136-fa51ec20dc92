<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LocalEps</class>
 <widget class="QWidget" name="LocalEps">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>646</width>
    <height>391</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>本地漫画章节</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="4" column="0">
    <layout class="QGridLayout" name="gridLayout_3">
     <item row="0" column="0">
      <widget class="ComicListWidget" name="bookList">
       <property name="styleSheet">
        <string notr="true"/>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="0" column="0">
    <widget class="QLabel" name="name">
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item row="2" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QRadioButton" name="showWaifu2x">
       <property name="text">
        <string>只显示Waifu2x</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ComicListWidget</class>
   <extends>QListWidget</extends>
   <header location="global">component.list.comic_list_widget.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
 <slots>
  <slot>JumpPage()</slot>
  <slot>RefreshDataFocus()</slot>
 </slots>
</ui>
