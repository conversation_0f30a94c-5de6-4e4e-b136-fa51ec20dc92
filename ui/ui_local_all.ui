<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LocalAll</class>
 <widget class="QWidget" name="LocalAll">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>541</width>
    <height>293</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>批量下载</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <item>
      <widget class="QPushButton" name="selectAllButton">
       <property name="maximumSize">
        <size>
         <width>150</width>
         <height>30</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string>一键全选/反选</string>
       </property>
       <property name="shortcut">
        <string>Return</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="downButton">
       <property name="maximumSize">
        <size>
         <width>150</width>
         <height>30</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string>删除</string>
       </property>
       <property name="shortcut">
        <string>Return</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="BaseTableWidget" name="tableWidget">
     <column>
      <property name="text">
       <string>选择</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>名称</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>分类</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>图片数</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>添加日期</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>上次观看时间</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>ID</string>
      </property>
     </column>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="msgLabel">
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3"/>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseTableWidget</class>
   <extends>QTableWidget</extends>
   <header location="global">component.tab.base_table_widget.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
