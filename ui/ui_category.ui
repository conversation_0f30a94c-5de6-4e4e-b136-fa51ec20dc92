<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Category</class>
 <widget class="QWidget" name="Category">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>分类</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTabWidget" name="tabWidget"/>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QComboBox" name="sortCombox">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="minimumSize">
        <size>
         <width>100</width>
         <height>0</height>
        </size>
       </property>
       <item>
        <property name="text">
         <string>最新</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>总排行</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>月排行</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>周排行</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>日排行</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>最多图片</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>最多爱心</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="Line" name="line_4">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="minimumSize">
        <size>
         <width>60</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>页：0/0</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="Line" name="line_5">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QSpinBox" name="spinBox">
       <property name="minimumSize">
        <size>
         <width>50</width>
         <height>0</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color:transparent;</string>
       </property>
       <property name="minimum">
        <number>1</number>
       </property>
       <property name="maximum">
        <number>1</number>
       </property>
      </widget>
     </item>
     <item>
      <widget class="Line" name="line_6">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="jumpPage">
       <property name="minimumSize">
        <size>
         <width>60</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>跳转</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
