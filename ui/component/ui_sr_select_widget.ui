<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SrSelect</class>
 <widget class="QWidget" name="SrSelect">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>673</width>
    <height>345</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>500</width>
    <height>0</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QLabel" name="label_4">
     <property name="text">
      <string>支持Waifu2x、Real-CUGAN、Real-ESRGAN（不支持CPU）</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label">
     <property name="text">
      <string>UP2X：采样倍率(建议选2X)，DENOISE：降噪等级(建议选最高),</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QTableWidget" name="tableWidget">
     <property name="editTriggers">
      <set>QAbstractItemView::NoEditTriggers</set>
     </property>
     <property name="selectionBehavior">
      <enum>QAbstractItemView::SelectRows</enum>
     </property>
     <property name="sortingEnabled">
      <bool>true</bool>
     </property>
     <attribute name="horizontalHeaderVisible">
      <bool>true</bool>
     </attribute>
     <attribute name="horizontalHeaderCascadingSectionResizes">
      <bool>false</bool>
     </attribute>
     <attribute name="horizontalHeaderDefaultSectionSize">
      <number>25</number>
     </attribute>
     <attribute name="horizontalHeaderHighlightSections">
      <bool>false</bool>
     </attribute>
     <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
      <bool>true</bool>
     </attribute>
     <attribute name="horizontalHeaderStretchLastSection">
      <bool>false</bool>
     </attribute>
     <attribute name="verticalHeaderVisible">
      <bool>true</bool>
     </attribute>
     <attribute name="verticalHeaderMinimumSectionSize">
      <number>23</number>
     </attribute>
     <attribute name="verticalHeaderDefaultSectionSize">
      <number>23</number>
     </attribute>
     <attribute name="verticalHeaderHighlightSections">
      <bool>true</bool>
     </attribute>
     <attribute name="verticalHeaderShowSortIndicator" stdset="0">
      <bool>false</bool>
     </attribute>
     <attribute name="verticalHeaderStretchLastSection">
      <bool>false</bool>
     </attribute>
     <column>
      <property name="text">
       <string>模型名</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>算法</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>耗时</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>描述</string>
      </property>
     </column>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QPushButton" name="saveButton">
       <property name="maximumSize">
        <size>
         <width>150</width>
         <height>30</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string>确定</string>
       </property>
       <property name="shortcut">
        <string>Return</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="closeButton">
       <property name="maximumSize">
        <size>
         <width>150</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>关闭</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../res/images.qrc"/>
 </resources>
 <connections/>
</ui>
