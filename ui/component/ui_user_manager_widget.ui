<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UserManagerWidget</class>
 <widget class="QWidget" name="UserManagerWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QScrollArea" name="scrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>390</width>
        <height>462</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QLabel" name="label_7">
         <property name="text">
          <string>如果你的邮件一直无法接受到验证连接</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_8">
         <property name="text">
          <string>请前往官方Discord频道-未收到验证信协助区</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QCommandLinkButton" name="commandLinkButton">
         <property name="text">
          <string>官方Discord</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="Line" name="line">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>重新发送邮箱验证</string>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>邮箱：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="verfyEdit"/>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QPushButton" name="verfyButton">
         <property name="text">
          <string>发送</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Minimum</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="Line" name="line_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_3">
         <property name="text">
          <string>重置密码</string>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>邮箱：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="resetEdit"/>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QPushButton" name="resetButton">
         <property name="text">
          <string>发送</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Minimum</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="Line" name="line_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_5">
         <property name="text">
          <string>账号验证（如果你无法打开邮箱里的验证地址，可以复制到此处验证）</string>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <item>
          <widget class="QLabel" name="label_6">
           <property name="text">
            <string>地址：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="sendEdit"/>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QPushButton" name="sendButton">
         <property name="text">
          <string>发送</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
