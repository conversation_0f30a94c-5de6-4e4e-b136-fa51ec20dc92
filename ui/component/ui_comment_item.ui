<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CommentItem</class>
 <widget class="QWidget" name="CommentItem">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>658</width>
    <height>213</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QToolButton
{
background-color:transparent;
  border: 0px;
  height: 0px;
  margin: 0px;
  padding: 0px;
  border-right: 0px;
  border-left: 0px;
}
QToolButton:hover  {
background-color:transparent;
  border-right: 0px;
  border-left: 0px;
}

QToolButton:pressed  {
background-color:transparent;
  border-right: 0px;
  border-left: 0px;
}

QToolButton:checked  {
background-color:transparent;
  border-right: 0px;
  border-left: 0px;
}</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="indexLabel">
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>100</width>
             <height>30</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
           <property name="layoutDirection">
            <enum>Qt::RightToLeft</enum>
           </property>
           <property name="styleSheet">
            <string notr="true">color: #999999;</string>
           </property>
           <property name="text">
            <string>TextLabel</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item alignment="Qt::AlignHCenter">
        <widget class="HeadLabel" name="picIcon">
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>100</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>100</width>
           <height>100</height>
          </size>
         </property>
         <property name="text">
          <string>TextLabel</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="dateLabel">
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>100</width>
           <height>30</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>10</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">color: #999999;</string>
         </property>
         <property name="text">
          <string>TextLabel</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_3">
         <item>
          <widget class="QLabel" name="nameLabel">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>20</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>20</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
           <property name="layoutDirection">
            <enum>Qt::LeftToRight</enum>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>TextLabel</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_4">
           <item>
            <widget class="QLabel" name="levelLabel">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>20</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>20</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(196, 95, 125);</string>
             </property>
             <property name="text">
              <string>LV</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="titleLabel">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>20</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>20</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(196, 95, 125);</string>
             </property>
             <property name="text">
              <string>TextLabel</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_3">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QLabel" name="commentLabel">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Minimum</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>10</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <property name="sizeConstraint">
            <enum>QLayout::SetDefaultConstraint</enum>
           </property>
           <item>
            <widget class="QToolButton" name="starButton">
             <property name="minimumSize">
              <size>
               <width>35</width>
               <height>35</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">background-color:transparent;</string>
             </property>
             <property name="text">
              <string>...</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QToolButton" name="commentButton">
             <property name="minimumSize">
              <size>
               <width>35</width>
               <height>35</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">background-color:transparent;</string>
             </property>
             <property name="text">
              <string>...</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_2">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>30</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QLabel" name="linkLabel">
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>40</height>
          </size>
         </property>
         <property name="text">
          <string>TextLabel</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>HeadLabel</class>
   <extends>QLabel</extends>
   <header location="global">component.label.head_label.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>commentButton</sender>
   <signal>clicked()</signal>
   <receiver>CommentItem</receiver>
   <slot>OpenComment()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>208</x>
     <y>147</y>
    </hint>
    <hint type="destinationlabel">
     <x>257</x>
     <y>241</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>AddLike()</slot>
  <slot>OpenComment()</slot>
  <slot>KillComment()</slot>
 </slots>
</ui>
