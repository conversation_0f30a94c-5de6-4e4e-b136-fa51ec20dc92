<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ComicItem</class>
 <widget class="QWidget" name="ComicItem">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>300</width>
    <height>289</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QToolButton
{
background-color:transparent;
  border: 0px;
  height: 0px;
  margin: 0px;
  padding: 0px;
  border-right: 0px;
  border-left: 0px;
}
QToolButton:hover  {
background-color:transparent;
  border-right: 0px;
  border-left: 0px;
}

QToolButton:pressed  {
background-color:transparent;
  border-right: 0px;
  border-left: 0px;
}

QToolButton:checked  {
background-color:transparent;
  border-right: 0px;
  border-left: 0px;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <widget class="QLabel" name="picLabel">
     <property name="text">
      <string>TextLabel</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::Minimum</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>10</width>
       <height>10</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QLabel" name="categoryLabel">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>30</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">color: rgb(133,133,133);
</string>
     </property>
     <property name="text">
      <string>TextLabel</string>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item alignment="Qt::AlignLeft">
      <widget class="QToolButton" name="starButton">
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(133,133,133);
background-color:transparent;</string>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="timeLabel">
       <property name="text">
        <string/>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLabel" name="nameLable">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="text">
      <string>TextLabel</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
