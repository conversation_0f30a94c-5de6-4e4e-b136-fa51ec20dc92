<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LoginProxyWidget</class>
 <widget class="QWidget" name="LoginProxyWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>541</width>
    <height>483</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>450</width>
    <height>0</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>代理设置</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="spacing">
    <number>12</number>
   </property>
   <item row="0" column="0">
    <widget class="SmoothScrollArea" name="scrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>504</width>
        <height>598</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_11">
         <item>
          <widget class="QRadioButton" name="proxy_0">
           <property name="text">
            <string>无代理</string>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioProxyGroup</string>
           </attribute>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QRadioButton" name="proxy_1">
           <property name="minimumSize">
            <size>
             <width>90</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>HTTP代理</string>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioProxyGroup</string>
           </attribute>
          </widget>
         </item>
         <item>
          <widget class="Line" name="line">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label">
           <property name="text">
            <string>代理地址</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="httpLine">
           <property name="toolTip">
            <string>http://127.0.0.1:10809</string>
           </property>
           <property name="placeholderText">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_10">
         <item>
          <widget class="QRadioButton" name="proxy_2">
           <property name="minimumSize">
            <size>
             <width>90</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>Sock5代理</string>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioProxyGroup</string>
           </attribute>
          </widget>
         </item>
         <item>
          <widget class="Line" name="line_2">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_5">
           <property name="text">
            <string>代理地址</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="sockEdit">
           <property name="toolTip">
            <string>127.0.0.1:10808</string>
           </property>
           <property name="placeholderText">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_12">
         <item>
          <widget class="QRadioButton" name="proxy_3">
           <property name="text">
            <string>使用系统代理</string>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioProxyGroup</string>
           </attribute>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="checkLabel">
           <property name="font">
            <font>
             <bold>false</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">color:rgb(255, 0, 0)</string>
           </property>
           <property name="text">
            <string>未检测到系统代理</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="proxyLabel">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <widget class="Line" name="line_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <item>
          <widget class="QLabel" name="label_12">
           <property name="text">
            <string>API超时时间  </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="WheelComboBox" name="apiTimeout">
           <item>
            <property name="text">
             <string>2</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>5</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>7</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_10">
           <property name="text">
            <string>图片超时时间 </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="WheelComboBox" name="imgTimeout">
           <item>
            <property name="text">
             <string>2</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>5</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>7</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>10</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>15</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_4">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_9">
         <item>
          <widget class="QCheckBox" name="httpsBox">
           <property name="text">
            <string>启用Https（如果出现连接被重置，建议关闭试试）</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QRadioButton" name="loginProxy">
         <property name="text">
          <string>使用注册分流（无法注册可尝试开启）</string>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <item>
          <widget class="QLabel" name="label_9">
           <property name="minimumSize">
            <size>
             <width>90</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>UA设置:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="uaRandom">
           <property name="text">
            <string>随机生成</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QLineEdit" name="uaEdit"/>
       </item>
       <item>
        <widget class="QPushButton" name="testSpeedButton">
         <property name="text">
          <string>测速</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="Line" name="line_4">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QGridLayout" name="gridLayout_2">
         <item row="4" column="1">
          <widget class="QLabel" name="label_api_3">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item row="4" column="2">
          <widget class="QRadioButton" name="radio_img_3">
           <property name="text">
            <string>分流3</string>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioImgGroup</string>
           </attribute>
          </widget>
         </item>
         <item row="5" column="1">
          <widget class="QLabel" name="label_api_4">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item row="5" column="0">
          <widget class="QRadioButton" name="radioButton_4">
           <property name="toolTip">
            <string>所有分流不可使用时，自动解锁</string>
           </property>
           <property name="text">
            <string>分流4</string>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioApiGroup</string>
           </attribute>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLabel" name="label_api_1">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item row="2" column="2">
          <widget class="QRadioButton" name="radio_img_1">
           <property name="text">
            <string>分流1</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioImgGroup</string>
           </attribute>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QLabel" name="label_api_2">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>Api分流</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QRadioButton" name="radioButton_1">
           <property name="text">
            <string>分流1</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioApiGroup</string>
           </attribute>
          </widget>
         </item>
         <item row="1" column="3">
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>速度</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="3" column="3">
          <widget class="QLabel" name="label_img_2">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLabel" name="label_6">
           <property name="text">
            <string>延迟</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="5" column="2">
          <widget class="QRadioButton" name="radio_img_4">
           <property name="toolTip">
            <string>所有分流不可使用时，自动解锁</string>
           </property>
           <property name="text">
            <string>分流4</string>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioImgGroup</string>
           </attribute>
          </widget>
         </item>
         <item row="4" column="0">
          <widget class="QRadioButton" name="radioButton_3">
           <property name="text">
            <string>分流3</string>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioApiGroup</string>
           </attribute>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QLabel" name="label_7">
           <property name="text">
            <string>图片分流</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="7" column="1">
          <widget class="QLabel" name="label_api_5">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item row="5" column="3">
          <widget class="QLabel" name="label_img_4">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item row="7" column="0">
          <widget class="QRadioButton" name="radioButton_5">
           <property name="text">
            <string>CDN分流</string>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioApiGroup</string>
           </attribute>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QRadioButton" name="radioButton_2">
           <property name="text">
            <string>分流2</string>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioApiGroup</string>
           </attribute>
          </widget>
         </item>
         <item row="3" column="2">
          <widget class="QRadioButton" name="radio_img_2">
           <property name="text">
            <string>分流2</string>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioImgGroup</string>
           </attribute>
          </widget>
         </item>
         <item row="7" column="2">
          <widget class="QRadioButton" name="radio_img_5">
           <property name="text">
            <string>CDN分流</string>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioImgGroup</string>
           </attribute>
          </widget>
         </item>
         <item row="7" column="3">
          <widget class="QLabel" name="label_img_5">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item row="4" column="3">
          <widget class="QLabel" name="label_img_3">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item row="2" column="3">
          <widget class="QLabel" name="label_img_1">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item row="6" column="0">
          <widget class="QRadioButton" name="radioButton_6">
           <property name="text">
            <string>US反代分流</string>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioApiGroup</string>
           </attribute>
          </widget>
         </item>
         <item row="6" column="2">
          <widget class="QRadioButton" name="radio_img_6">
           <property name="text">
            <string>US反代分流</string>
           </property>
           <attribute name="buttonGroup">
            <string notr="true">radioImgGroup</string>
           </attribute>
          </widget>
         </item>
         <item row="6" column="1">
          <widget class="QLabel" name="label_api_6">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item row="6" column="3">
          <widget class="QLabel" name="label_img_6">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="label_11">
           <property name="text">
            <string> CDN地址:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="cdn_api_ip">
           <property name="minimumSize">
            <size>
             <width>120</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>120</width>
             <height>16777215</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_8">
           <property name="text">
            <string>CDN地址:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="cdn_img_ip">
           <property name="minimumSize">
            <size>
             <width>120</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>120</width>
             <height>16777215</height>
            </size>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_8">
         <item>
          <widget class="QLabel" name="label_3">
           <property name="text">
            <string>CDN设置请看说明获取</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCommandLinkButton" name="commandLinkButton">
           <property name="text">
            <string>说明</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>SmoothScrollArea</class>
   <extends>QScrollArea</extends>
   <header location="global">component.scroll_area.smooth_scroll_area.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>WheelComboBox</class>
   <extends>QComboBox</extends>
   <header location="global">component.box.wheel_combo_box.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>testSpeedButton</sender>
   <signal>clicked()</signal>
   <receiver>LoginProxyWidget</receiver>
   <slot>SpeedTest()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>73</x>
     <y>81</y>
    </hint>
    <hint type="destinationlabel">
     <x>458</x>
     <y>47</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>Login()</slot>
  <slot>OpenRegister()</slot>
  <slot>SpeedTest()</slot>
  <slot>SaveSetting()</slot>
  <slot>OpenUrl()</slot>
 </slots>
 <buttongroups>
  <buttongroup name="radioApiGroup"/>
  <buttongroup name="radioProxyGroup"/>
  <buttongroup name="radioImgGroup"/>
 </buttongroups>
</ui>
