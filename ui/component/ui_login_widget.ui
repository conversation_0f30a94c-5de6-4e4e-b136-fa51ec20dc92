<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LoginWidget</class>
 <widget class="QWidget" name="LoginWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="spacing">
    <number>12</number>
   </property>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QLabel" name="label_2">
     <property name="text">
      <string>如果不能连接和看图，请尝试选择其他分流。</string>
     </property>
     <property name="wordWrap">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label_5">
     <property name="text">
      <string>用户名</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLineEdit" name="userEdit_2"/>
   </item>
   <item>
    <widget class="QLabel" name="label_6">
     <property name="text">
      <string>密码</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLineEdit" name="passwdEdit_2">
     <property name="echoMode">
      <enum>QLineEdit::Password</enum>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QCheckBox" name="saveBox">
       <property name="text">
        <string>保存密码</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="autoBox">
       <property name="text">
        <string>自动登录</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="autoSign">
       <property name="text">
        <string>自动打卡</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer_2">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
