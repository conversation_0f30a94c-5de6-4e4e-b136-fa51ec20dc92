<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Exit</class>
 <widget class="QWidget" name="Exit">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>231</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QWidget" name="widget" native="true">
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <widget class="QRadioButton" name="radioButton1">
        <property name="text">
         <string>退出</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
        <attribute name="buttonGroup">
         <string notr="true">buttonGroup</string>
        </attribute>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="radioButton2">
        <property name="text">
         <string>最小化到任务栏</string>
        </property>
        <property name="checked">
         <bool>false</bool>
        </property>
        <attribute name="buttonGroup">
         <string notr="true">buttonGroup</string>
        </attribute>
       </widget>
      </item>
      <item>
       <widget class="QCheckBox" name="checkBox">
        <property name="text">
         <string>不在每次弹出该窗口（可在设置中设置）</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QPushButton" name="button">
       <property name="maximumSize">
        <size>
         <width>150</width>
         <height>30</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string>确定</string>
       </property>
       <property name="shortcut">
        <string>Return</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
 <buttongroups>
  <buttongroup name="buttonGroup"/>
 </buttongroups>
</ui>
