<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Waifu2xTool</class>
 <widget class="QWidget" name="Waifu2xTool">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>705</width>
    <height>498</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>图片查看</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QGraphicsView" name="graphicsView"/>
   </item>
   <item row="0" column="1">
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <widget class="SmoothScrollArea" name="scrollArea">
       <property name="maximumSize">
        <size>
         <width>300</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="widgetResizable">
        <bool>true</bool>
       </property>
       <widget class="QWidget" name="scrollAreaWidgetContents">
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>281</width>
          <height>480</height>
         </rect>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_4">
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_2">
           <item>
            <widget class="Line" name="line_5">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="checkBox">
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>waifu2x</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item alignment="Qt::AlignLeft">
            <widget class="QCheckBox" name="ttaModel">
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="toolTip">
              <string>画质提升，耗时增加</string>
             </property>
             <property name="text">
              <string>tta模式</string>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_3">
             <item alignment="Qt::AlignLeft">
              <widget class="QRadioButton" name="scaleRadio">
               <property name="maximumSize">
                <size>
                 <width>100</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>倍数放大</string>
               </property>
               <property name="checked">
                <bool>true</bool>
               </property>
               <attribute name="buttonGroup">
                <string notr="true">buttonGroup_2</string>
               </attribute>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="scaleEdit">
               <property name="maximumSize">
                <size>
                 <width>160</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>2</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_4">
             <item>
              <widget class="QRadioButton" name="heighRadio">
               <property name="maximumSize">
                <size>
                 <width>100</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>固定长宽</string>
               </property>
               <attribute name="buttonGroup">
                <string notr="true">buttonGroup_2</string>
               </attribute>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="widthEdit">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="maximumSize">
                <size>
                 <width>60</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_2">
               <property name="maximumSize">
                <size>
                 <width>20</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>X</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="heighEdit">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="maximumSize">
                <size>
                 <width>60</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_6">
             <item>
              <widget class="QLabel" name="label_5">
               <property name="maximumSize">
                <size>
                 <width>60</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>模型：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="modelName">
               <property name="font">
                <font>
                 <pointsize>8</pointsize>
                </font>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_7">
             <item>
              <widget class="QLabel" name="label_3">
               <property name="maximumSize">
                <size>
                 <width>60</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>格式：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QComboBox" name="fmtComboBox">
               <item>
                <property name="text">
                 <string>自动</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>JPG</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>PNG</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>BMP</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>WEBP</string>
                </property>
               </item>
              </widget>
             </item>
            </layout>
           </item>
           <item alignment="Qt::AlignHCenter">
            <widget class="QPushButton" name="changeButton">
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>转换</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_8">
             <item>
              <widget class="QLabel" name="label_8">
               <property name="maximumSize">
                <size>
                 <width>60</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>分辨率：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="resolutionLabel">
               <property name="maximumSize">
                <size>
                 <width>160</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>无信息</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_9">
             <item>
              <widget class="QLabel" name="label_10">
               <property name="maximumSize">
                <size>
                 <width>60</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>大 小：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="sizeLabel">
               <property name="maximumSize">
                <size>
                 <width>160</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>无信息</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_11">
             <item alignment="Qt::AlignLeft">
              <widget class="QLabel" name="label_9">
               <property name="maximumSize">
                <size>
                 <width>60</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>转换模式：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="gpuLabel">
               <property name="maximumSize">
                <size>
                 <width>150</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>GPU</string>
               </property>
               <property name="wordWrap">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout">
             <item>
              <widget class="QLabel" name="label">
               <property name="maximumSize">
                <size>
                 <width>60</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>格式</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="format">
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_10">
             <item alignment="Qt::AlignLeft">
              <widget class="QLabel" name="label_6">
               <property name="maximumSize">
                <size>
                 <width>60</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>耗时：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="tickLabel">
               <property name="maximumSize">
                <size>
                 <width>160</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_2">
             <item>
              <widget class="QPushButton" name="oepnButton">
               <property name="maximumSize">
                <size>
                 <width>100</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>打开图片</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="saveButton">
               <property name="maximumSize">
                <size>
                 <width>100</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>保存图片</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_5">
             <item>
              <widget class="QPushButton" name="pushButton">
               <property name="maximumSize">
                <size>
                 <width>100</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>放大</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_3">
               <property name="maximumSize">
                <size>
                 <width>100</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>缩小</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item alignment="Qt::AlignHCenter">
            <widget class="QPushButton" name="headButton">
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>设置为头像</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>SmoothScrollArea</class>
   <extends>QScrollArea</extends>
   <header location="global">component.scroll_area.smooth_scroll_area.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>checkBox</sender>
   <signal>clicked()</signal>
   <receiver>Waifu2xTool</receiver>
   <slot>SwithPicture()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>371</x>
     <y>42</y>
    </hint>
    <hint type="destinationlabel">
     <x>386</x>
     <y>545</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>saveButton</sender>
   <signal>clicked()</signal>
   <receiver>Waifu2xTool</receiver>
   <slot>SavePicture()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>346</x>
     <y>461</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>378</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>heighEdit</sender>
   <signal>textChanged(QString)</signal>
   <receiver>Waifu2xTool</receiver>
   <slot>CheckScaleRadio()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>488</x>
     <y>123</y>
    </hint>
    <hint type="destinationlabel">
     <x>386</x>
     <y>545</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton_3</sender>
   <signal>clicked()</signal>
   <receiver>Waifu2xTool</receiver>
   <slot>ReduceScalePic()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>346</x>
     <y>401</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>300</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>heighRadio</sender>
   <signal>clicked()</signal>
   <receiver>Waifu2xTool</receiver>
   <slot>CheckScaleRadio()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>343</x>
     <y>123</y>
    </hint>
    <hint type="destinationlabel">
     <x>221</x>
     <y>545</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>ttaModel</sender>
   <signal>clicked()</signal>
   <receiver>Waifu2xTool</receiver>
   <slot>CheckScaleRadio()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>371</x>
     <y>68</y>
    </hint>
    <hint type="destinationlabel">
     <x>386</x>
     <y>545</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>oepnButton</sender>
   <signal>clicked()</signal>
   <receiver>Waifu2xTool</receiver>
   <slot>OpenPicture()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>346</x>
     <y>363</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>204</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>widthEdit</sender>
   <signal>textChanged(QString)</signal>
   <receiver>Waifu2xTool</receiver>
   <slot>CheckScaleRadio()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>408</x>
     <y>123</y>
    </hint>
    <hint type="destinationlabel">
     <x>250</x>
     <y>545</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton</sender>
   <signal>clicked()</signal>
   <receiver>Waifu2xTool</receiver>
   <slot>AddScalePic()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>346</x>
     <y>431</y>
    </hint>
    <hint type="destinationlabel">
     <x>232</x>
     <y>406</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>scaleEdit</sender>
   <signal>textChanged(QString)</signal>
   <receiver>Waifu2xTool</receiver>
   <slot>CheckScaleRadio()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>488</x>
     <y>95</y>
    </hint>
    <hint type="destinationlabel">
     <x>386</x>
     <y>545</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>scaleRadio</sender>
   <signal>clicked()</signal>
   <receiver>Waifu2xTool</receiver>
   <slot>CheckScaleRadio()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>343</x>
     <y>95</y>
    </hint>
    <hint type="destinationlabel">
     <x>179</x>
     <y>545</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>changeButton</sender>
   <signal>clicked()</signal>
   <receiver>Waifu2xTool</receiver>
   <slot>StartWaifu2x()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>346</x>
     <y>220</y>
    </hint>
    <hint type="destinationlabel">
     <x>386</x>
     <y>312</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>headButton</sender>
   <signal>clicked()</signal>
   <receiver>Waifu2xTool</receiver>
   <slot>SetHead()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>346</x>
     <y>491</y>
    </hint>
    <hint type="destinationlabel">
     <x>380</x>
     <y>491</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>StartWaifu2x()</slot>
  <slot>AddScalePic()</slot>
  <slot>ReduceScalePic()</slot>
  <slot>OpenPicture()</slot>
  <slot>SavePicture()</slot>
  <slot>SwithPicture()</slot>
  <slot>ChangeModel()</slot>
  <slot>CheckScaleRadio()</slot>
  <slot>SetHead()</slot>
 </slots>
 <buttongroups>
  <buttongroup name="buttonGroup_2"/>
 </buttongroups>
</ui>
