<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Favorite</class>
 <widget class="QWidget" name="Favorite">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>628</width>
    <height>334</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>收藏</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="0" column="0">
    <layout class="QGridLayout" name="gridLayout_3">
     <item row="0" column="0">
      <widget class="ComicListWidget" name="bookList">
       <property name="styleSheet">
        <string notr="true"/>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <layout class="QGridLayout" name="gridLayout_4">
     <item row="0" column="0">
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QLabel" name="msgLabel">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="folderBox">
         <property name="minimumSize">
          <size>
           <width>120</width>
           <height>0</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="sortCombox">
         <item>
          <property name="text">
           <string>收藏时间</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>更新时间</string>
          </property>
         </item>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="Line" name="line_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="nums">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="text">
          <string>收藏数：</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="pages">
         <property name="text">
          <string>页</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="Line" name="line">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="Line" name="line_4">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QSpinBox" name="spinBox">
         <property name="minimumSize">
          <size>
           <width>50</width>
           <height>30</height>
          </size>
         </property>
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>1</number>
         </property>
        </widget>
       </item>
       <item>
        <widget class="Line" name="line_3">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="jumpButton">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="text">
          <string>跳转</string>
         </property>
         <property name="shortcut">
          <string>Return</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ComicListWidget</class>
   <extends>QListWidget</extends>
   <header location="global">component.list.comic_list_widget.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>jumpButton</sender>
   <signal>clicked()</signal>
   <receiver>Favorite</receiver>
   <slot>JumpPage()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>439</x>
     <y>280</y>
    </hint>
    <hint type="destinationlabel">
     <x>361</x>
     <y>291</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>JumpPage()</slot>
  <slot>RefreshDataFocus()</slot>
 </slots>
</ui>
