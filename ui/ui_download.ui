<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Download</class>
 <widget class="QWidget" name="Download">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1002</width>
    <height>510</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>下载</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="5" column="0">
    <layout class="QGridLayout" name="gridLayout">
     <item row="5" column="0">
      <widget class="QTableWidget" name="tableWidget">
       <column>
        <property name="text">
         <string>id</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>时间</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>标题</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>下载进度</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>下载章节</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>下载速度</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>下载状态</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>转换进度</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>转换章节</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>转换耗时</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>转换状态</string>
        </property>
       </column>
      </widget>
     </item>
    </layout>
   </item>
   <item row="2" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QCheckBox" name="webp2Png">
       <property name="text">
        <string>Webp保存为Png</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="redownloadRadio">
       <property name="text">
        <string>下载失败后1分钟自动重试</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="skipPic">
       <property name="text">
        <string>超分跳过损坏图片</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="radioButton">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="text">
        <string>下载自动进行图片超分</string>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_5">
     <item>
      <widget class="QComboBox" name="comboBox">
       <item>
        <property name="text">
         <string>全部</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>未完成</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>已完成</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEdit"/>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="someDownButton">
       <property name="text">
        <string>批量下载</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton">
       <property name="text">
        <string>全部开始下载</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_3">
       <property name="text">
        <string>全部暂停下载</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_2">
       <property name="text">
        <string>全部开始转换</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_4">
       <property name="text">
        <string>全部暂停转换</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>pushButton</sender>
   <signal>clicked()</signal>
   <receiver>Download</receiver>
   <slot>StartAll()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>94</x>
     <y>29</y>
    </hint>
    <hint type="destinationlabel">
     <x>52</x>
     <y>-19</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton_3</sender>
   <signal>clicked()</signal>
   <receiver>Download</receiver>
   <slot>StopAll()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>189</x>
     <y>27</y>
    </hint>
    <hint type="destinationlabel">
     <x>142</x>
     <y>-42</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton_2</sender>
   <signal>clicked()</signal>
   <receiver>Download</receiver>
   <slot>StartConvertAll()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>307</x>
     <y>27</y>
    </hint>
    <hint type="destinationlabel">
     <x>274</x>
     <y>-41</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton_4</sender>
   <signal>clicked()</signal>
   <receiver>Download</receiver>
   <slot>StopConvertAll()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>397</x>
     <y>22</y>
    </hint>
    <hint type="destinationlabel">
     <x>374</x>
     <y>-51</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>radioButton</sender>
   <signal>clicked()</signal>
   <receiver>Download</receiver>
   <slot>SetAutoConvert()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>495</x>
     <y>24</y>
    </hint>
    <hint type="destinationlabel">
     <x>492</x>
     <y>-33</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>StartAll()</slot>
  <slot>StopAll()</slot>
  <slot>StartConvertAll()</slot>
  <slot>StopConvertAll()</slot>
  <slot>SetAutoConvert()</slot>
 </slots>
</ui>
