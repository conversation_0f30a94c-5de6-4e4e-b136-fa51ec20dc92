<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Help</class>
 <widget class="QWidget" name="Help">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>773</width>
    <height>738</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>帮助</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QListWidget {background-color:transparent;}
QScrollArea {background-color:transparent;}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="SmoothScrollArea" name="scrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>753</width>
        <height>718</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QWidget" name="widget" native="true">
           <layout class="QVBoxLayout" name="verticalLayout_2">
            <item>
             <spacer name="verticalSpacer_2">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item alignment="Qt::AlignHCenter">
             <widget class="QLabel" name="label">
              <property name="text">
               <string/>
              </property>
              <property name="pixmap">
               <pixmap resource="../res/images.qrc">:/png/icon/icon_comment.png</pixmap>
              </property>
             </widget>
            </item>
            <item alignment="Qt::AlignHCenter">
             <widget class="QLabel" name="label_2">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>需要反馈使用过程中的问题？</string>
              </property>
             </widget>
            </item>
            <item alignment="Qt::AlignHCenter">
             <widget class="QLabel" name="label_3">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>想提供一些建议？</string>
              </property>
             </widget>
            </item>
            <item alignment="Qt::AlignHCenter">
             <widget class="QPushButton" name="pushButton">
              <property name="text">
               <string>Github Issue</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QWidget" name="widget_2" native="true">
              <layout class="QGridLayout" name="gridLayout">
               <item row="0" column="1">
                <widget class="QLabel" name="version">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>v1.2.8</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="2">
                <widget class="QCheckBox" name="preCheckBox">
                 <property name="text">
                  <string>接受Beta版本更新</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_9">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>上次更新时间：</string>
                 </property>
                </widget>
               </item>
               <item row="4" column="1">
                <widget class="QLabel" name="localTime">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string/>
                 </property>
                </widget>
               </item>
               <item row="0" column="0">
                <widget class="QLabel" name="label_4">
                 <property name="minimumSize">
                  <size>
                   <width>80</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>版本号:</string>
                 </property>
                </widget>
               </item>
               <item row="3" column="1">
                <widget class="QLabel" name="configVer">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string/>
                 </property>
                </widget>
               </item>
               <item row="5" column="2">
                <widget class="QPushButton" name="logButton">
                 <property name="text">
                  <string>打开日志目录</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="2">
                <widget class="QPushButton" name="verCheck">
                 <property name="text">
                  <string>检测更新</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QLabel" name="upTimeLabel">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>2021-11-27</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="0">
                <widget class="QLabel" name="label_6">
                 <property name="minimumSize">
                  <size>
                   <width>80</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>图片超分版本:</string>
                 </property>
                </widget>
               </item>
               <item row="5" column="0">
                <widget class="QLabel" name="label_7">
                 <property name="minimumSize">
                  <size>
                   <width>80</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>日志:</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <widget class="QLabel" name="waifu2x">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>v1.0.8</string>
                 </property>
                </widget>
               </item>
               <item row="5" column="1">
                <widget class="QPushButton" name="openCmd">
                 <property name="text">
                  <string>打开控制台</string>
                 </property>
                </widget>
               </item>
               <item row="3" column="0">
                <widget class="QLabel" name="label_10">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>配置版本</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QWidget" name="updateWidget" native="true">
              <layout class="QVBoxLayout" name="verticalLayout_4">
               <item>
                <widget class="QLabel" name="label_8">
                 <property name="font">
                  <font>
                   <pointsize>16</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>新版本：</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="updateLabel">
                 <property name="text">
                  <string/>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="updateButton">
                 <property name="text">
                  <string>前往更新</string>
                 </property>
                 <property name="icon">
                  <iconset resource="../res/images.qrc">
                   <normaloff>:/png/icon/new.svg</normaloff>:/png/icon/new.svg</iconset>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <spacer name="verticalSpacer">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>SmoothScrollArea</class>
   <extends>QScrollArea</extends>
   <header location="global">component.scroll_area.smooth_scroll_area.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../res/images.qrc"/>
 </resources>
 <connections/>
</ui>
