<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>NasAdd</class>
 <widget class="QWidget" name="NasAdd">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>897</width>
    <height>515</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QGridLayout" name="gridLayout_2">
     <item row="0" column="2">
      <widget class="QComboBox" name="comboBox">
       <item>
        <property name="text">
         <string>WebDav</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>SMB</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>本地路径</string>
        </property>
       </item>
      </widget>
     </item>
     <item row="5" column="2">
      <widget class="QLineEdit" name="pathEdit">
       <property name="text">
        <string>/测试/长篇/</string>
       </property>
       <property name="placeholderText">
        <string/>
       </property>
      </widget>
     </item>
     <item row="3" column="0">
      <widget class="QLabel" name="label_port">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>端口</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="label_address">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>地址</string>
       </property>
      </widget>
     </item>
     <item row="6" column="2">
      <widget class="QLineEdit" name="userEdit">
       <property name="text">
        <string>test</string>
       </property>
       <property name="placeholderText">
        <string/>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="label_8">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>存储名/别名</string>
       </property>
      </widget>
     </item>
     <item row="8" column="2">
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QRadioButton" name="compress1Radio">
         <property name="text">
          <string>Zip</string>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
         <attribute name="buttonGroup">
          <string notr="true">compressGroup</string>
         </attribute>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="compress2Radio">
         <property name="text">
          <string>cbz(带漫画信息)</string>
         </property>
         <attribute name="buttonGroup">
          <string notr="true">compressGroup</string>
         </attribute>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item row="5" column="0">
      <widget class="QLabel" name="label_path">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>路径</string>
       </property>
      </widget>
     </item>
     <item row="1" column="2">
      <widget class="QLineEdit" name="titleEdit">
       <property name="toolTip">
        <string>长篇</string>
       </property>
       <property name="text">
        <string>长篇</string>
       </property>
       <property name="placeholderText">
        <string/>
       </property>
      </widget>
     </item>
     <item row="2" column="2">
      <widget class="QLineEdit" name="addressEdit">
       <property name="text">
        <string>http://*************</string>
       </property>
       <property name="placeholderText">
        <string/>
       </property>
      </widget>
     </item>
     <item row="7" column="0">
      <widget class="QLabel" name="label_pass">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>密码</string>
       </property>
      </widget>
     </item>
     <item row="0" column="0">
      <widget class="QLabel" name="label">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>协议</string>
       </property>
      </widget>
     </item>
     <item row="4" column="0">
      <widget class="QLabel" name="label_select">
       <property name="text">
        <string>选择文件夹</string>
       </property>
      </widget>
     </item>
     <item row="7" column="2">
      <widget class="QLineEdit" name="passEdit">
       <property name="text">
        <string>test</string>
       </property>
       <property name="placeholderText">
        <string/>
       </property>
      </widget>
     </item>
     <item row="10" column="0">
      <widget class="QLabel" name="label_6">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>90</height>
        </size>
       </property>
       <property name="text">
        <string>其他设置</string>
       </property>
      </widget>
     </item>
     <item row="6" column="0">
      <widget class="QLabel" name="label_user">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>用户名</string>
       </property>
      </widget>
     </item>
     <item row="8" column="0">
      <widget class="QLabel" name="label_5">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>打包方式</string>
       </property>
      </widget>
     </item>
     <item row="10" column="2">
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QCheckBox" name="isWaifu2x">
           <property name="text">
            <string>使用下载中超分后图片</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item row="9" column="2">
      <layout class="QHBoxLayout" name="horizontalLayout_5">
       <item>
        <widget class="QRadioButton" name="dir1Radio">
         <property name="text">
          <string>不单独新增目录</string>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
         <attribute name="buttonGroup">
          <string notr="true">buttonGroup</string>
         </attribute>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="dir2Radio">
         <property name="text">
          <string>每本漫画单独目录</string>
         </property>
         <attribute name="buttonGroup">
          <string notr="true">buttonGroup</string>
         </attribute>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="dir3Radio">
         <property name="text">
          <string>按添加日期分目录</string>
         </property>
         <attribute name="buttonGroup">
          <string notr="true">buttonGroup</string>
         </attribute>
        </widget>
       </item>
      </layout>
     </item>
     <item row="3" column="2">
      <widget class="QLineEdit" name="portEdit">
       <property name="text">
        <string>5005</string>
       </property>
      </widget>
     </item>
     <item row="9" column="0">
      <widget class="QLabel" name="label_10">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>目录设置</string>
       </property>
      </widget>
     </item>
     <item row="4" column="2">
      <layout class="QHBoxLayout" name="horizontalLayout_6">
       <item>
        <widget class="QPushButton" name="dirButton">
         <property name="text">
          <string>选择文件夹</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLabel" name="label_7">
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <property name="sizeConstraint">
      <enum>QLayout::SetDefaultConstraint</enum>
     </property>
     <item>
      <widget class="QPushButton" name="saveButton">
       <property name="maximumSize">
        <size>
         <width>150</width>
         <height>30</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string>确定</string>
       </property>
       <property name="shortcut">
        <string>Return</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="testButton">
       <property name="maximumSize">
        <size>
         <width>150</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>测试连接</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="closeButton">
       <property name="maximumSize">
        <size>
         <width>150</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>关闭</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>comboBox</tabstop>
  <tabstop>titleEdit</tabstop>
  <tabstop>addressEdit</tabstop>
  <tabstop>pathEdit</tabstop>
  <tabstop>userEdit</tabstop>
  <tabstop>passEdit</tabstop>
  <tabstop>compress1Radio</tabstop>
  <tabstop>dir1Radio</tabstop>
  <tabstop>dir2Radio</tabstop>
  <tabstop>dir3Radio</tabstop>
  <tabstop>isWaifu2x</tabstop>
  <tabstop>saveButton</tabstop>
  <tabstop>testButton</tabstop>
  <tabstop>closeButton</tabstop>
 </tabstops>
 <resources/>
 <connections/>
 <buttongroups>
  <buttongroup name="compressGroup"/>
  <buttongroup name="buttonGroup"/>
 </buttongroups>
</ui>
