<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Index</class>
 <widget class="QWidget" name="Index">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>595</width>
    <height>279</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>首页</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="spacing">
    <number>0</number>
   </property>
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>最新上传</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="ComicListWidget" name="newListWidget"/>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>383</width>
          <height>27</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="pages">
        <property name="text">
         <string>页</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="Line" name="line_3">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="spinBox">
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>30</height>
         </size>
        </property>
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>1</number>
        </property>
       </widget>
      </item>
      <item>
       <widget class="Line" name="line">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="jumpButton">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <property name="text">
         <string>跳转</string>
        </property>
        <property name="shortcut">
         <string>Return</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ComicListWidget</class>
   <extends>QListWidget</extends>
   <header location="global">component.list.comic_list_widget.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
