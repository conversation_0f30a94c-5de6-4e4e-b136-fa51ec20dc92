<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Week</class>
 <widget class="QWidget" name="Week">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>423</width>
    <height>311</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>每周必看</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QComboBox" name="comboBox">
       <property name="minimumSize">
        <size>
         <width>400</width>
         <height>0</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>每周五 18:00更新</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>日漫</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="ComicListWidget" name="mangaWidget"/>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_2">
      <attribute name="title">
       <string>韩漫</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <widget class="ComicListWidget" name="hanmanWidget"/>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_3">
      <attribute name="title">
       <string>其他</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_4">
       <item>
        <widget class="ComicListWidget" name="anotherWidget"/>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ComicListWidget</class>
   <extends>QListWidget</extends>
   <header location="global">component.list.comic_list_widget.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
