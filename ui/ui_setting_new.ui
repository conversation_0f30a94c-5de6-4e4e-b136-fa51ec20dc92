<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SettingNew</class>
 <widget class="QWidget" name="SettingNew">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>880</width>
    <height>805</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>设置</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item alignment="Qt::AlignLeft">
    <widget class="QLabel" name="msgLabel">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="styleSheet">
      <string notr="true">color: rgb(255, 0, 0);</string>
     </property>
     <property name="text">
      <string>你修改的部分设置，需要重启生效</string>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QCommandLinkButton" name="generalButton">
         <property name="text">
          <string>通用</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QCommandLinkButton" name="proxyButton">
         <property name="text">
          <string>代理</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QCommandLinkButton" name="waifu2xButton">
         <property name="text">
          <string>Waifu2x</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QCommandLinkButton" name="downloadButton">
         <property name="text">
          <string>下载与缓存</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <widget class="SmoothScrollArea" name="scrollArea">
       <property name="styleSheet">
        <string notr="true">.QFrame
         {
         background-color: rgb(253, 253, 253);

         border:2px solid rgb(234,234,234);
         border-radius:5px
         }        </string>
       </property>
       <property name="widgetResizable">
        <bool>true</bool>
       </property>
       <widget class="QWidget" name="scrollAreaWidgetContents">
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>-2082</y>
          <width>661</width>
          <height>2871</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_4">
         <item>
          <widget class="QLabel" name="generalLabel">
           <property name="font">
            <font>
             <pointsize>18</pointsize>
            </font>
           </property>
           <property name="text">
            <string>通用</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_1">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_3">
            <item>
             <widget class="QLabel" name="label_update">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>19</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>更新：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="checkBox_IsUpdate">
              <property name="text">
               <string>启动时检查更新</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_16">
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_20">
            <item>
             <widget class="QLabel" name="label_37">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>19</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>触控（重启生效）（如果出现点击图片位置错乱，请使用100%缩放）：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="grabGestureBox">
              <property name="text">
               <string>启用触控优化</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_2">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_5">
            <item>
             <widget class="QLabel" name="label_6">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>21</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>主题：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="themeButton0">
              <property name="text">
               <string>跟随系统</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">themeGroup</string>
              </attribute>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="themeButton2">
              <property name="text">
               <string>白</string>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">themeGroup</string>
              </attribute>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="themeButton1">
              <property name="text">
               <string>黑</string>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">themeGroup</string>
              </attribute>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_3">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_6">
            <item>
             <widget class="QLabel" name="label_lang">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>语言：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="languageButton0">
              <property name="text">
               <string>根据系统</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">languageGroup</string>
              </attribute>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="languageButton1">
              <property name="text">
               <string>简体中文</string>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">languageGroup</string>
              </attribute>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="languageButton2">
              <property name="text">
               <string>繁體中文</string>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">languageGroup</string>
              </attribute>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="languageButton3">
              <property name="text">
               <string>English</string>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">languageGroup</string>
              </attribute>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_7">
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_18">
            <item>
             <widget class="QLabel" name="label_8">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>字体（重启生效）：</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_3">
              <item>
               <widget class="QLabel" name="label_33">
                <property name="text">
                 <string>字体选择：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="WheelComboBox" name="fontBox">
                <property name="minimumSize">
                 <size>
                  <width>150</width>
                  <height>0</height>
                 </size>
                </property>
                <item>
                 <property name="text">
                  <string>默认</string>
                 </property>
                </item>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_23">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_25">
              <item>
               <widget class="QLabel" name="label_34">
                <property name="text">
                 <string>字体大小：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="WheelComboBox" name="fontSize">
                <property name="minimumSize">
                 <size>
                  <width>150</width>
                  <height>0</height>
                 </size>
                </property>
                <item>
                 <property name="text">
                  <string>默认</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>9</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>12</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>14</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>16</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>18</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>20</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>22</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>24</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>26</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>28</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>30</string>
                 </property>
                </item>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_24">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_26">
              <item>
               <widget class="QLabel" name="label_35">
                <property name="text">
                 <string>字体粗细：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="WheelComboBox" name="fontStyle">
                <property name="minimumSize">
                 <size>
                  <width>150</width>
                  <height>0</height>
                 </size>
                </property>
                <item>
                 <property name="text">
                  <string>默认</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>高亮</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>正常</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>半粗体</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>粗体</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>黑体</string>
                 </property>
                </item>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_25">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_13">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_16">
            <item>
             <widget class="QLabel" name="label">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>界面缩放显示（需重启生效）：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="mainScaleBox">
              <property name="text">
               <string>是否使用自定义缩放比例</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_4">
              <item>
               <widget class="QLabel" name="label_13">
                <property name="maximumSize">
                 <size>
                  <width>150</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>缩放比例：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="WheelSpinBox" name="scaleBox">
                <property name="minimum">
                 <number>50</number>
                </property>
                <property name="maximum">
                 <number>300</number>
                </property>
                <property name="value">
                 <number>100</number>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_17">
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_21">
            <item>
             <widget class="QLabel" name="label_38">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>关闭设置</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="showCloseButton0">
              <property name="text">
               <string>关闭后退出</string>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">showCloseButtonGroup</string>
              </attribute>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="showCloseButton1">
              <property name="text">
               <string>关闭后最小化到托盘</string>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">showCloseButtonGroup</string>
              </attribute>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_14">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_17">
            <item>
             <widget class="QLabel" name="label_2">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>封面显示大小（默认为100%）：</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_15">
              <item>
               <widget class="WheelSpinBox" name="coverSize">
                <property name="minimumSize">
                 <size>
                  <width>140</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="minimum">
                 <number>50</number>
                </property>
                <property name="maximum">
                 <number>250</number>
                </property>
                <property name="singleStep">
                 <number>10</number>
                </property>
                <property name="value">
                 <number>100</number>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_13">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QLabel" name="label_29">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>分类封面大小：</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_22">
              <item>
               <widget class="WheelSpinBox" name="categorySize">
                <property name="minimumSize">
                 <size>
                  <width>140</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="minimum">
                 <number>30</number>
                </property>
                <property name="maximum">
                 <number>250</number>
                </property>
                <property name="singleStep">
                 <number>10</number>
                </property>
                <property name="value">
                 <number>80</number>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_21">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QLabel" name="label_40">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>标题显示行数</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_30">
              <item>
               <widget class="WheelComboBox" name="titleLineBox">
                <item>
                 <property name="text">
                  <string>不显示</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>1行</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>2行</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>3行</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>全部</string>
                 </property>
                </item>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_28">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QLabel" name="label_41">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>分类显示</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_31">
              <item>
               <widget class="WheelComboBox" name="categoryBox">
                <item>
                 <property name="text">
                  <string>显示</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>不显示</string>
                 </property>
                </item>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_29">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QLabel" name="label_42">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>图片浏览设置</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="openglBox">
              <property name="text">
               <string>开启OpenGL加速</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_4">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_7">
            <item>
             <widget class="QLabel" name="label_10">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>21</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>日志等级：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="logutton0">
              <property name="text">
               <string>Warn</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">logGroup</string>
              </attribute>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="logutton1">
              <property name="text">
               <string>Info</string>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">logGroup</string>
              </attribute>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="logutton2">
              <property name="text">
               <string>Debug</string>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">logGroup</string>
              </attribute>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="Line" name="line">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_3">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Minimum</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="proxyLabel">
           <property name="font">
            <font>
             <pointsize>18</pointsize>
            </font>
           </property>
           <property name="text">
            <string>代理</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_5">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_8">
            <item>
             <widget class="QLabel" name="label_7">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>26</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Http代理：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="proxy0">
              <property name="text">
               <string>无代理</string>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">proxyGroup</string>
              </attribute>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_23">
              <item>
               <widget class="QRadioButton" name="proxy1">
                <property name="minimumSize">
                 <size>
                  <width>90</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="text">
                 <string>HTTP代理</string>
                </property>
                <attribute name="buttonGroup">
                 <string notr="true">proxyGroup</string>
                </attribute>
               </widget>
              </item>
              <item>
               <widget class="Line" name="line_5">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_31">
                <property name="text">
                 <string>代理地址</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="httpEdit"/>
              </item>
              <item>
               <spacer name="horizontalSpacer">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_24">
              <item>
               <widget class="QRadioButton" name="proxy2">
                <property name="minimumSize">
                 <size>
                  <width>90</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="text">
                 <string>Sock5代理</string>
                </property>
                <attribute name="buttonGroup">
                 <string notr="true">proxyGroup</string>
                </attribute>
               </widget>
              </item>
              <item>
               <widget class="Line" name="line_6">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_32">
                <property name="text">
                 <string>代理地址</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="sockEdit"/>
              </item>
              <item>
               <spacer name="horizontalSpacer_22">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_27">
              <item>
               <widget class="QRadioButton" name="proxy3">
                <property name="text">
                 <string>使用系统代理</string>
                </property>
                <attribute name="buttonGroup">
                 <string notr="true">proxyGroup</string>
                </attribute>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_28">
              <item>
               <widget class="QPushButton" name="openProxy">
                <property name="text">
                 <string>打开分流设置</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_26">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_6">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_9">
            <item>
             <widget class="QLabel" name="label_9">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>19</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>聊天室：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="chatProxy">
              <property name="text">
               <string>启用代理</string>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="Line" name="line_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="Line" name="line_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_2">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Minimum</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="waifu2xLabel">
           <property name="font">
            <font>
             <pointsize>18</pointsize>
            </font>
           </property>
           <property name="text">
            <string>Waifu2x设置</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_10">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_12">
            <item>
             <widget class="QLabel" name="label_20">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>21</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>CPU/GPU选择（需重启生效）</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_17">
              <item>
               <widget class="WheelComboBox" name="encodeSelect">
                <property name="minimumSize">
                 <size>
                  <width>150</width>
                  <height>0</height>
                 </size>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_14">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QLabel" name="label_11">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>21</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>使用CPU数量（CPU模式生效，需重启）</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_16">
              <item>
               <widget class="WheelComboBox" name="threadSelect">
                <property name="minimumSize">
                 <size>
                  <width>150</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="focusPolicy">
                 <enum>Qt::StrongFocus</enum>
                </property>
                <item>
                 <property name="text">
                  <string>Auto</string>
                 </property>
                </item>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_15">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QLabel" name="label_39">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Tile大小，降低可减少显存占用（如出现部分图片无法转换可调低该值）</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_29">
              <item>
               <widget class="WheelComboBox" name="tileComboBox">
                <item>
                 <property name="text">
                  <string>Auto</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>200</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>100</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>32</string>
                 </property>
                </item>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_27">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_8">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_10">
            <item>
             <widget class="QLabel" name="label_12">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>84</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Waifu2x看图模式</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="readCheckBox">
              <property name="text">
               <string>是否启用</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="preDownWaifu2x">
              <property name="text">
               <string>优先使用下载转换好的缓存</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_21">
              <item>
               <widget class="QLabel" name="label_28">
                <property name="text">
                 <string>为了保证速度，图片分辨率小于等于该值时才进行转换（默认4096P）</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="WheelSpinBox" name="lookMaxBox">
                <property name="minimumSize">
                 <size>
                  <width>80</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="minimum">
                 <number>360</number>
                </property>
                <property name="maximum">
                 <number>10000</number>
                </property>
                <property name="value">
                 <number>4096</number>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_20">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_5">
              <item>
               <widget class="QLabel" name="label_14">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>156</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>模型</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QToolButton" name="readModelName">
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_3">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_6">
              <item>
               <widget class="QLabel" name="label_15">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>156</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>放大倍数</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="WheelDoubleSpinBox" name="readScale">
                <property name="minimumSize">
                 <size>
                  <width>150</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="decimals">
                 <number>1</number>
                </property>
                <property name="maximum">
                 <double>32.000000000000000</double>
                </property>
                <property name="singleStep">
                 <double>0.100000000000000</double>
                </property>
                <property name="value">
                 <double>2.000000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_4">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_12">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_15">
            <item>
             <widget class="QLabel" name="label_24">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>84</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Waifu2x封面模式（开启后所有封面会经过Waifu2x处理）</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="coverCheckBox">
              <property name="text">
               <string>是否启用</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_11">
              <item>
               <widget class="QLabel" name="label_22">
                <property name="text">
                 <string>为了保证速度，封面分辨率小于等于该值时才进行转换（默认400P）</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="WheelSpinBox" name="coverMaxBox">
                <property name="minimumSize">
                 <size>
                  <width>80</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="minimum">
                 <number>100</number>
                </property>
                <property name="maximum">
                 <number>2560</number>
                </property>
                <property name="value">
                 <number>400</number>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_9">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_13">
              <item>
               <widget class="QLabel" name="label_25">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>156</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>模型</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QToolButton" name="coverModelName">
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_11">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_14">
              <item>
               <widget class="QLabel" name="label_26">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>156</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>放大倍数</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="WheelDoubleSpinBox" name="coverScale">
                <property name="minimumSize">
                 <size>
                  <width>150</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="decimals">
                 <number>1</number>
                </property>
                <property name="maximum">
                 <double>32.000000000000000</double>
                </property>
                <property name="singleStep">
                 <double>0.100000000000000</double>
                </property>
                <property name="value">
                 <double>2.000000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_12">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_9">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_11">
            <item>
             <widget class="QLabel" name="label_16">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>110</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>waifu2x下载模式（开启后下载完成会经过Waifu2x处理）</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="downAuto">
              <property name="text">
               <string>下载完后自动转换</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_9">
              <item>
               <widget class="QLabel" name="label_18">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>156</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>模型</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QToolButton" name="downModelName">
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_6">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_7">
              <item>
               <widget class="QLabel" name="label_19">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>156</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>放大倍数</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="WheelDoubleSpinBox" name="downScale">
                <property name="minimumSize">
                 <size>
                  <width>150</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="decimals">
                 <number>1</number>
                </property>
                <property name="maximum">
                 <double>32.000000000000000</double>
                </property>
                <property name="singleStep">
                 <double>0.100000000000000</double>
                </property>
                <property name="value">
                 <double>2.000000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_7">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="Line" name="line_4">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_4">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Minimum</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="downloadLabel">
           <property name="font">
            <font>
             <pointsize>18</pointsize>
            </font>
           </property>
           <property name="text">
            <string>下载与缓存</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_15">
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_19">
            <item>
             <widget class="QLabel" name="label_36">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>32</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>保存路径格式设置（新下载的文件才能生效）</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="saveNameButton0">
              <property name="text">
               <string>作品名（默认）</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">saveNameGroup</string>
              </attribute>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="saveNameButton1">
              <property name="text">
               <string>[作者名]作品名</string>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">saveNameGroup</string>
              </attribute>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="saveNameButton2">
              <property name="text">
               <string>作者名单独目录（如无作者名将放入default目录）</string>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">saveNameGroup</string>
              </attribute>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame">
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_14">
            <item>
             <widget class="QLabel" name="label_17">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>JM图片解密进程数量（对应下载线程数量，需重启）</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_8">
              <item>
               <widget class="WheelSpinBox" name="threadSpin">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="minimum">
                 <number>2</number>
                </property>
                <property name="maximum">
                 <number>40</number>
                </property>
                <property name="value">
                 <number>8</number>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_2">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_11">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_13">
            <item>
             <widget class="QLabel" name="label_21">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>32</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>下载和缓存路径（缓存文件需自己手动清除）</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_10">
              <item>
               <widget class="QPushButton" name="setDirButton">
                <property name="text">
                 <string>设置目录</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_8">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_2">
              <item>
               <widget class="QLabel" name="label_3">
                <property name="minimumSize">
                 <size>
                  <width>80</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="text">
                 <string>下载</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="downloadDir">
                <property name="minimumSize">
                 <size>
                  <width>150</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="openDownloadDir">
                <property name="text">
                 <string>打开目录</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_16">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_18">
              <item>
               <widget class="QLabel" name="label_4">
                <property name="minimumSize">
                 <size>
                  <width>80</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="text">
                 <string>缓存</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="cacheDir">
                <property name="minimumSize">
                 <size>
                  <width>150</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="openCacheDir">
                <property name="text">
                 <string>打开目录</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_17">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_20">
              <item>
               <widget class="QLabel" name="label_27">
                <property name="minimumSize">
                 <size>
                  <width>80</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="text">
                 <string>聊天缓存</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="chatDir">
                <property name="minimumSize">
                 <size>
                  <width>150</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="openChatDir">
                <property name="text">
                 <string>打开目录</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_18">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_19">
              <item>
               <widget class="QLabel" name="label_5">
                <property name="minimumSize">
                 <size>
                  <width>80</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="text">
                 <string>Waifu2x缓存</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="waifu2xDir">
                <property name="minimumSize">
                 <size>
                  <width>150</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="openWaifu2xDir">
                <property name="text">
                 <string>打开目录</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_19">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>SmoothScrollArea</class>
   <extends>QScrollArea</extends>
   <header location="global">component.scroll_area.smooth_scroll_area.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>WheelComboBox</class>
   <extends>QComboBox</extends>
   <header location="global">component.box.wheel_combo_box.h</header>
  </customwidget>
  <customwidget>
   <class>WheelDoubleSpinBox</class>
   <extends>QDoubleSpinBox</extends>
   <header location="global">component.box.wheel_double_spin_box.h</header>
  </customwidget>
  <customwidget>
   <class>WheelSpinBox</class>
   <extends>QSpinBox</extends>
   <header location="global">component.box.wheel_spin_box.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
 <buttongroups>
  <buttongroup name="proxyGroup"/>
  <buttongroup name="themeGroup"/>
  <buttongroup name="showCloseButtonGroup"/>
  <buttongroup name="saveNameGroup"/>
  <buttongroup name="logGroup"/>
  <buttongroup name="languageGroup"/>
 </buttongroups>
</ui>
