<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>BookEps</class>
 <widget class="QWidget" name="BookEps">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>章节下载</string>
  </property>

  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QLabel" name="nameLabel">
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>章节</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="selectButton">
       <property name="text">
        <string>全选</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="cancleButton">
       <property name="text">
        <string>反选</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="downloadButton">
       <property name="text">
        <string>下载</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="BaseListWidget" name="listWidget">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseListWidget</class>
   <extends>QListWidget</extends>
   <header location="global">component.list.base_list_widget.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
