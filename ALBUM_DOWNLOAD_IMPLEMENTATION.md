# JMComic-qt 专辑下载功能实现

## 概述

本实现将JMComic-qt项目的单图片HTTP下载替换为使用第三方`jmcomic`库的批量专辑下载功能，同时保持与现有系统的完全兼容性。

## 主要修改

### 1. 核心下载系统 (`src/task/task_download.py`)

#### 新增功能：
- **DownloadAlbum方法**: 使用jmcomic库下载整个专辑
- **专辑下载线程**: `_DownloadAlbumThread` 在独立线程中执行专辑下载
- **进度跟踪**: `_SetupProgressTracking` 实现实时进度更新
- **jmcomic配置**: `_CreateJmcomicOption` 创建下载选项

#### 关键特性：
- 自动检测jmcomic库可用性，不可用时回退到原始下载方式
- 支持图片解码（反混淆）
- 自动检测图片格式
- 与现有回调系统完全兼容

### 2. 任务基类扩展 (`src/task/qt_task.py`)

#### 新增方法：
- **AddDownloadAlbum**: 添加专辑下载任务的接口方法

### 3. 下载状态管理 (`src/view/download/download_status.py`)

#### 智能下载策略：
- **_ShouldUseAlbumDownload**: 自动判断是否使用专辑下载
- **StartItemAlbumDownload**: 启动专辑下载流程
- **专辑下载回调**: 完整的进度、状态和完成回调支持

#### 回退机制：
- 专辑下载失败时自动回退到原始单图片下载方式

### 4. 下载项目扩展 (`src/view/download/download_item.py`)

#### 新增方法：
- **GetAlbumSavePath**: 为专辑下载生成合适的保存路径

## 技术实现细节

### 专辑下载流程

1. **检测阶段**: 系统自动检测是否应该使用专辑下载
2. **配置阶段**: 创建jmcomic下载选项，包括路径规则和图片处理设置
3. **下载阶段**: 在独立线程中执行专辑下载，避免阻塞UI
4. **进度跟踪**: 通过包装下载方法实现实时进度更新
5. **完成处理**: 更新数据库状态，调用完成回调

### 兼容性保证

- **向后兼容**: 所有现有接口保持不变
- **数据库兼容**: 不修改现有数据库结构
- **UI兼容**: 进度更新通过现有回调机制传播到UI
- **错误处理**: 专辑下载失败时自动回退到原始方式

### 配置选项

专辑下载使用以下jmcomic配置：

```python
{
    'dir_rule': {
        'rule': 'Bd_Td',  # BookId_Title格式
        'base_dir': '用户设置的保存路径或默认路径'
    },
    'download': {
        'image': {
            'decode': True,    # 启用图片解码（反混淆）
            'suffix': 'auto'   # 自动检测图片格式
        }
    }
}
```

## 使用方式

### 自动使用

系统会在以下情况自动使用专辑下载：
- jmcomic库可用
- 从专辑开始下载（非恢复特定图片）
- 非特定章节/图片下载

### 手动调用

```python
# 通过QtTaskBase
task_id = self.AddDownloadAlbum(
    bookId="123456",
    statusBack=status_callback,
    downloadCallBack=progress_callback,
    completeCallBack=complete_callback,
    savePath="/path/to/save"
)

# 直接调用TaskDownload
from task.task_download import TaskDownload
downloader = TaskDownload()
task_id = downloader.DownloadAlbum(
    bookId="123456",
    statusBack=status_callback,
    downloadCallBack=progress_callback,
    completeCallBack=complete_callback,
    savePath="/path/to/save"
)
```

## 测试

运行测试脚本验证实现：

```bash
python test_album_download.py
```

测试覆盖：
- jmcomic库可用性检测
- 新增方法和类的正确性
- 模块导入和基本功能
- 路径生成逻辑

## 优势

1. **性能提升**: 批量下载比逐个图片下载更高效
2. **完整性保证**: 专辑级下载确保所有图片完整下载
3. **自动解码**: 内置图片反混淆功能
4. **智能回退**: 失败时自动使用原始下载方式
5. **零破坏性**: 完全保持现有功能和接口

## 依赖要求

- `jmcomic >= 2.4.3` (已在requirements.txt中)
- 现有的所有依赖保持不变

## 注意事项

1. **线程安全**: 专辑下载在独立线程中执行，避免阻塞主线程
2. **内存管理**: 大型专辑下载时注意内存使用
3. **错误处理**: 网络错误或其他异常会自动回退到原始下载方式
4. **路径管理**: 专辑下载会创建结构化的目录结构

## 未来扩展

- 支持更多jmcomic配置选项
- 添加专辑下载的暂停/恢复功能
- 优化大型专辑的内存使用
- 添加下载速度限制选项
