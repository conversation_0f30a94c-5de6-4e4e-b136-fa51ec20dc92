[project]
name = "jmcomic-qt"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.10"
dependencies = [
    "bs4>=0.0.2",
    "curl-cffi>=0.12.0",
    "httpx[http2,socks]>=0.28.1",
    "jmcomic>=2.4.3",
    "lxml>=6.0.0",
    "natsort>=8.4.0",
    "pillow>=11.3.0",
    "pycryptodomex>=3.23.0",
    "pyside6==6.5.3",
    "pysmb>=1.2.11",
    "pysocks>=1.7.1",
    "sr-ncnn-vulkan",
    "tqdm>=4.67.1",
    "webdavclient3>=3.14.6",
]

[tool.uv.sources]
sr-ncnn-vulkan = { url = "https://github.com/tonquer/waifu2x-vulkan/releases/download/v1.1.6/sr_ncnn_vulkan-1.2.0-cp36.cp37.cp38.cp39.cp310.cp311-none-win_amd64.whl" }
