import hashlib
import os
from functools import partial
import threading
import time

from config import config
from config.setting import Setting
from task.qt_task import TaskBase, QtTaskBase
from tools.book import BookMgr, BookEps
from tools.log import Log
from tools.status import Status
from tools.str import Str

# Import jmcomic for album download functionality
try:
    import jmcomic
    JMCOMIC_AVAILABLE = True
except ImportError:
    JMCOMIC_AVAILABLE = False
    Log.Warn("jmcomic library not available, falling back to individual image downloads")


class QtDownloadTask(object):
    Waiting = Str.Waiting
    Reading = Str.Reading
    ReadingEps = Str.ReadingEps
    ReadingPicture = Str.ReadingPicture
    Downloading = Str.Downloading
    Success = Str.Success
    Error = Str.Error
    Cache = Str.Cache

    def __init__(self, downloadId=0):
        self.downloadId = downloadId
        self.downloadCallBack = None       # addData, laveSize
        self.downloadCompleteBack = None   # data, status
        self.statusBack = None
        self.fileSize = 0
        self.url = ""
        self.path = ""
        self.originalName = ""
        self.backParam = None
        self.cleanFlag = ""
        self.isInit = False
        self.isReload = False
        self.lastLaveSize = 0
        self.isLoadTask = False
        self.isAlbumDownload = False  # New flag for album downloads

        self.loadPath = ""    # 只加载
        self.cachePath = ""   # 缓存路径
        self.savePath = ""    # 下载保存路径
        self.saveParam = ""    # 下载保存路径

        self.bookId = ""      # 下载的bookId
        self.epsIndex = 0        # 下载的章节
        self.index = 0        # 下载的索引
        self.resetCnt = 0     # 重试次数
        self.isLocal = True
        self.status = self.Waiting

        # Album download specific fields
        self.albumProgress = {"current": 0, "total": 0, "current_photo": 0, "total_photos": 0}


class TaskDownload(TaskBase, QtTaskBase):

    def __init__(self):
        TaskBase.__init__(self)
        QtTaskBase.__init__(self)
        self.taskObj.downloadBack.connect(self.HandlerTask)
        self.taskObj.downloadStBack.connect(self.HandlerTaskSt)
        self.thread.start()

    def Run(self):
        while True:
            v = self._inQueue.get(True)
            self._inQueue.task_done()
            if v == "":
                break
            self.HandlerDownload({"st": Status.Ok}, (v, QtDownloadTask.Waiting))

    def DownloadTask(self, url, downloadCallBack=None, completeCallBack=None, downloadStCallBack=None, backParam=None, loadPath="", cachePath="", savePath="", saveParam="", cleanFlag="", isReload=False, resetCnt=1):
        self.taskId += 1
        data = QtDownloadTask(self.taskId)
        data.downloadCallBack = downloadCallBack
        data.downloadCompleteBack = completeCallBack
        data.statusBack = downloadStCallBack
        data.backParam = backParam
        data.isReload = isReload
        data.url = url
        data.loadPath = loadPath
        data.cachePath = cachePath
        data.savePath = savePath
        data.saveParam = saveParam
        self.tasks[self.taskId] = data
        if cleanFlag:
            data.cleanFlag = cleanFlag
            taskIds = self.flagToIds.setdefault(cleanFlag, set())
            taskIds.add(self.taskId)

        Log.Debug("add download info, cachePath:{}, loadPath:{}, savePath:{}".format(data.cachePath, data.loadPath, data.savePath))
        from server.server import Server
        from server import req
        Server().Download(req.DownloadBookReq(url,  data.loadPath, data.cachePath, data.savePath, data.saveParam, data.isReload, resetCnt), backParams=self.taskId)
        return self.taskId

    def DownloadCache(self, filePath, completeCallBack=None, backParam = 0, cleanFlag=""):
        self.taskId += 1
        data = QtDownloadTask(self.taskId)
        data.downloadCompleteBack = completeCallBack
        data.loadPath = filePath
        data.backParam = backParam
        data.isLoadTask = True
        if cleanFlag:
            data.cleanFlag = cleanFlag
            taskIds = self.flagToIds.setdefault(cleanFlag, set())
            taskIds.add(self.taskId)
        self.tasks[self.taskId] = data
        self._inQueue.put(self.taskId)
        return self.taskId

    def HandlerTask(self, downloadId, addSize, laveFileSize, data, isCallBack=True):
        info = self.tasks.get(downloadId)
        if not info:
            return
        assert isinstance(info, QtDownloadTask)

        # 表示保存失败了
        if laveFileSize == -2:
            v = {"st": Status.SaveError}
            self.CallBookBack(v, info)
            return
        # 表示成功但是没有图片
        st = Str.Error
        if laveFileSize < -2:
            st = - laveFileSize

        if laveFileSize < 0 and data == b"":
            try:
                if info.downloadCompleteBack:
                    if info.backParam is not None:
                        info.downloadCompleteBack(b"", st, info.backParam)
                    else:
                        info.downloadCompleteBack(b"", st)
            except Exception as es:
                Log.Error(es)
            self.ClearDownloadTask(downloadId)
            return

        if info.lastLaveSize <= 0:
            info.lastLaveSize = laveFileSize

        if info.downloadCallBack:
            try:
                if info.backParam is not None:
                    info.downloadCallBack(addSize, laveFileSize, info.backParam)
                else:
                    info.downloadCallBack(addSize, laveFileSize)
            except Exception as es:
                Log.Error(es)
            info.lastLaveSize = laveFileSize

        if laveFileSize == 0 and data != b"":
            if info.downloadCompleteBack:
                try:
                    if info.cleanFlag:
                        taskIds = self.flagToIds.get(info.cleanFlag, set())
                        taskIds.discard(info.downloadId)
                    if info.backParam is not None:
                        info.downloadCompleteBack(data, Status.Ok, info.backParam)
                    else:
                        info.downloadCompleteBack(data, Status.Ok)
                except Exception as es:
                    Log.Error(es)
            self.ClearDownloadTask(downloadId)

    def DownloadBook(self, bookId, epsIndex, index, statusBack=None, downloadCallBack=None, completeCallBack=None,
                    backParam=None, loadPath="", cachePath="", savePath="", cleanFlag=None, isInit=False):
        self.taskId += 1
        data = QtDownloadTask(self.taskId)
        data.downloadCallBack = downloadCallBack
        data.downloadCompleteBack = completeCallBack
        data.statusBack = statusBack
        data.backParam = backParam
        data.bookId = bookId
        data.epsIndex = epsIndex
        data.index = index
        data.loadPath = loadPath
        data.cachePath = cachePath
        data.savePath = savePath
        data.isInit = isInit
        self.tasks[self.taskId] = data
        if cleanFlag:
            data.cleanFlag = cleanFlag
            taskIds = self.flagToIds.setdefault(cleanFlag, set())
            taskIds.add(self.taskId)
        Log.Debug("add download info, savePath:{}, loadPath:{}".format(data.savePath, data.loadPath))
        self._inQueue.put(self.taskId)
        return self.taskId

    def DownloadAlbum(self, bookId, statusBack=None, downloadCallBack=None, completeCallBack=None,
                     backParam=None, savePath="", cleanFlag=None):
        """
        Download entire album using jmcomic library
        """
        if not JMCOMIC_AVAILABLE:
            Log.Error("jmcomic library not available for album download")
            return None

        # Validate bookId
        if not bookId:
            Log.Error("bookId is required for album download")
            return None

        # Ensure bookId is string or can be converted to string
        try:
            book_id_str = str(bookId)
            if not book_id_str.strip():
                Log.Error("bookId cannot be empty string")
                return None
        except Exception as es:
            Log.Error(f"Cannot convert bookId to string: {es}")
            return None

        # Validate savePath
        if savePath and not isinstance(savePath, str):
            try:
                savePath = str(savePath)
            except Exception as es:
                Log.Error(f"Cannot convert savePath to string: {es}")
                savePath = ""

        self.taskId += 1
        data = QtDownloadTask(self.taskId)
        data.downloadCallBack = downloadCallBack
        data.downloadCompleteBack = completeCallBack
        data.statusBack = statusBack
        data.backParam = backParam
        data.bookId = book_id_str  # Use validated string
        data.savePath = savePath
        data.isAlbumDownload = True  # Mark as album download
        self.tasks[self.taskId] = data

        if cleanFlag:
            data.cleanFlag = cleanFlag
            taskIds = self.flagToIds.setdefault(cleanFlag, set())
            taskIds.add(self.taskId)

        Log.Debug("add album download info, bookId:{}, savePath:{}".format(book_id_str, savePath))
        self._inQueue.put(self.taskId)
        return self.taskId

    def HandlerDownload(self, data, v):
        (taskId, newStatus) = v
        task = self.tasks.get(taskId)
        if not task:
            return
        backData = {}
        from server import req, ToolUtil
        try:
            assert isinstance(task, QtDownloadTask)

            # Handle album download
            if hasattr(task, 'isAlbumDownload') and task.isAlbumDownload:
                self._HandleAlbumDownload(task, taskId, backData)
                return

            if task.isLoadTask:
                imgData = ToolUtil.LoadCachePicture(task.loadPath)
                if imgData:
                    TaskBase.taskObj.downloadBack.emit(taskId, 0, len(imgData), b"")
                    TaskBase.taskObj.downloadBack.emit(taskId, 0, 0, imgData)
                else:
                    TaskBase.taskObj.downloadBack.emit(taskId, 0, -Status.FileError, b"")
                return

            isReset = False
            if data["st"] != Status.Ok:
                task.resetCnt += 1

                # 失败了
                if task.resetCnt >= 5:
                    self.SetTaskStatus(taskId, backData, task.Error)
                    return

                isReset = True
            else:
                task.status = newStatus
            info = BookMgr().GetBook(task.bookId)
            if task.status == task.Waiting:
                isReset or self.SetTaskStatus(taskId, backData, task.Reading)
                if not info:
                    self.AddHttpTask(req.GetBookInfoReq2(task.bookId), self.HandlerDownload, (taskId, task.Reading))
                    return

                task.status = task.Reading
            if task.status == task.Reading:
                isReset or self.SetTaskStatus(taskId, backData, task.ReadingEps)

                epsInfo = info.pageInfo.epsInfo.get(task.epsIndex)
                if info.pageInfo.epsInfo  and not epsInfo:
                    self.SetTaskStatus(taskId, backData, Str.SpaceEps)
                    return

                if not epsInfo.pictureUrl:
                    self.AddHttpTask(req.GetBookEpsInfoReq2(task.bookId, epsInfo.epsId), self.HandlerDownload, (taskId, task.ReadingEps))
                    return

                assert isinstance(epsInfo, BookEps)
                task.status = task.ReadingEps
            if task.status == task.ReadingEps:
                isReset or self.SetTaskStatus(taskId, backData, task.ReadingPicture)

                epsInfo = info.pageInfo.epsInfo.get(task.epsIndex)
                assert isinstance(epsInfo, BookEps)
                if not epsInfo.scrambleId:
                    self.AddHttpTask(req.GetBookEpsScrambleReq2(task.bookId, epsInfo.index, epsInfo.epsId), self.HandlerDownload, (taskId, task.ReadingPicture))
                    return

                task.status = task.ReadingPicture
            if task.status == task.ReadingPicture:
                epsInfo = info.pageInfo.epsInfo.get(task.epsIndex)
                assert isinstance(epsInfo, BookEps)
                backData["maxPic"] = len(epsInfo.pictureUrl)
                backData["bookName"] = info.baseInfo.title
                backData["title"] = epsInfo.title
                backData["maxEps"] = len(info.pageInfo.epsInfo)
                backData["author"] = "&".join(info.baseInfo.authorList)
                isReset or self.SetTaskStatus(taskId, backData, task.Downloading)

                if task.isInit:
                    self.SetTaskStatus(taskId, backData, task.Success)
                    return

                pitureName = epsInfo.pictureName.get(task.index)
                task.saveParam = (epsInfo.epsId, epsInfo.scrambleId, pitureName)
                if task.savePath:
                    if ToolUtil.IsHaveFile(task.savePath):
                        self.SetTaskStatus(taskId, backData, task.Cache)
                        return
                else:
                    path = ToolUtil.GetRealPath(task.index+1, "book/{}/{}".format(task.bookId, task.epsIndex+1))
                    cachePath2 = os.path.join(os.path.join(Setting.SavePath.value, config.CachePathDir), path)
                    checkPaths = [task.loadPath]

                    if Setting.SavePath.value:
                        checkPaths.append(cachePath2)
                        task.cachePath = cachePath2

                    for cachePath in checkPaths:
                        if cachePath:
                            imgData = ToolUtil.LoadCachePicture(cachePath)
                            if imgData:
                                TaskBase.taskObj.downloadBack.emit(taskId, 0, len(imgData), b"")
                                TaskBase.taskObj.downloadBack.emit(taskId, 0, 0, imgData)
                                return

                imgUrl = epsInfo.pictureUrl.get(task.index)
                if not imgUrl:
                    self.SetTaskStatus(taskId, backData, task.Error)
                    return
                resetCnt = 3
                self.AddDownloadTask(imgUrl, "", task.downloadCallBack, task.downloadCompleteBack, task.statusBack,
                    task.backParam, task.loadPath, task.cachePath, task.savePath, task.saveParam, task.cleanFlag, resetCnt=resetCnt)
        except Exception as es:
            self.SetTaskStatus(taskId, backData, task.Error)
            Log.Error(es)
        return

    def SetTaskStatus(self, taskId, backData, status):
        backData["st"] = status
        self.taskObj.downloadStBack.emit(taskId, dict(backData))
        return

    def CallBookBack(self, data, task):
        try:
            if not task.statusBack:
                return
            if task.backParam is not None:
                task.statusBack(data, task.backParam)
            else:
                task.statusBack(data)
        except Exception as es:
            Log.Error(es)

    def HandlerTaskSt(self, downloadId, data):
        task = self.tasks.get(downloadId)
        if not task:
            return
        assert isinstance(task, QtDownloadTask)
        try:
            self.CallBookBack(data, task)
            status = task.status
            if status == task.Downloading or status == task.Error or status == task.Cache:
                self.ClearDownloadTask(downloadId)
        except Exception as es:
            Log.Error(es)

    def _HandleAlbumDownload(self, task, taskId, backData):
        """
        Handle album download using jmcomic library
        """
        try:
            self.SetTaskStatus(taskId, backData, task.Reading)

            # Start album download in a separate thread
            download_thread = threading.Thread(
                target=self._DownloadAlbumThread,
                args=(task, taskId, backData)
            )
            download_thread.daemon = True
            download_thread.start()

        except Exception as es:
            Log.Error(f"Album download error: {es}")
            self.SetTaskStatus(taskId, backData, task.Error)

    def _DownloadAlbumThread(self, task, taskId, backData):
        """
        Thread function for album download
        """
        try:
            # Validate task parameters
            if not task.bookId:
                Log.Error("Album download: bookId is empty")
                self.SetTaskStatus(taskId, backData, task.Error)
                return

            # Ensure bookId is string
            book_id = str(task.bookId)
            Log.Info(f"Starting album download for book: {book_id}")

            # Create jmcomic option
            option = self._CreateJmcomicOption(task)

            # Create custom downloader with progress tracking
            downloader = jmcomic.new_downloader(option)

            # Set up progress tracking
            self._SetupProgressTracking(downloader, task, taskId)

            # Download album
            self.SetTaskStatus(taskId, backData, task.Downloading)

            album = jmcomic.download_album(
                book_id,  # Use validated string book_id
                option=option,
                downloader=downloader
            )

            # Download completed successfully
            Log.Info(f"Album download completed for book: {book_id}")
            self.SetTaskStatus(taskId, backData, task.Success)

            # Call completion callback
            if task.downloadCompleteBack:
                try:
                    if task.backParam is not None:
                        task.downloadCompleteBack(b"", Status.Ok, task.backParam)
                    else:
                        task.downloadCompleteBack(b"", Status.Ok)
                except Exception as es:
                    Log.Error(f"Completion callback error: {es}")

            self.ClearDownloadTask(taskId)

        except Exception as es:
            Log.Error(f"Album download thread error for book {task.bookId}: {es}")
            import traceback
            Log.Error(f"Traceback: {traceback.format_exc()}")
            self.SetTaskStatus(taskId, backData, task.Error)

    def _CreateJmcomicOption(self, task):
        """
        Create jmcomic option for download
        """
        # Ensure base_dir is a string
        base_dir = task.savePath or Setting.SavePath.value or './downloads'
        if not isinstance(base_dir, str):
            base_dir = str(base_dir) if base_dir else './downloads'

        # Ensure bookId is a string
        book_id = str(task.bookId) if task.bookId else ''

        option_dict = {
            'dir_rule': {
                'rule': 'Bd_Td',  # BookId_Title
                'base_dir': base_dir
            },
            'download': {
                'image': {
                    'decode': True,  # Decode scrambled images
                    'suffix': 'auto'  # Auto detect image format
                }
            }
        }

        Log.Debug(f"Creating jmcomic option: base_dir={base_dir}, book_id={book_id}")
        return jmcomic.create_option(option_dict)

    def _SetupProgressTracking(self, downloader, task, taskId):
        """
        Setup progress tracking for jmcomic downloader
        """
        try:
            # Check if downloader has download_image method
            if not hasattr(downloader, 'download_image'):
                Log.Warn("Downloader does not have download_image method, skipping progress tracking")
                return

            # Store original download method
            original_download_image = downloader.download_image

            # Track progress
            progress_data = {'downloaded': 0, 'total': 0}

            def tracked_download_image(image_detail):
                """Wrapper for download_image with progress tracking"""
                try:
                    # Validate image_detail parameter
                    if image_detail is None:
                        Log.Warn("image_detail is None in tracked_download_image")
                        return original_download_image(image_detail)

                    # Call original method
                    result = original_download_image(image_detail)

                    # Update progress
                    progress_data['downloaded'] += 1

                    # Call progress callback
                    if task.downloadCallBack:
                        try:
                            # Simulate file size progress
                            downloaded_size = progress_data['downloaded'] * 1024  # 1KB per image
                            remaining_size = max(0, (progress_data['total'] - progress_data['downloaded']) * 1024)

                            if task.backParam is not None:
                                task.downloadCallBack(1024, remaining_size, task.backParam)
                            else:
                                task.downloadCallBack(1024, remaining_size)
                        except Exception as es:
                            Log.Error(f"Progress callback error: {es}")

                    return result

                except Exception as es:
                    Log.Error(f"Error in tracked_download_image: {es}")
                    # Fallback to original method
                    return original_download_image(image_detail)

            # Replace download method
            downloader.download_image = tracked_download_image

            # Store progress data reference
            task.albumProgress = progress_data

            Log.Debug("Progress tracking setup completed")

        except Exception as es:
            Log.Error(f"Progress tracking setup error: {es}")
            # Don't fail the download if progress tracking fails

    def _UpdateAlbumProgress(self, task, taskId, album, downloader):
        """
        Update album download progress (legacy method, kept for compatibility)
        """
        pass

    def ClearDownloadTask(self, downloadId):
        info = self.tasks.get(downloadId)
        if not info:
            return
        del self.tasks[downloadId]