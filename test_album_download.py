#!/usr/bin/env python3
"""
Test script for album download functionality
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_jmcomic_import():
    """Test if jmcomic library is available"""
    try:
        import jmcomic
        print("✓ jmcomic library is available")
        print(f"  Version: {getattr(jmcomic, '__version__', 'Unknown')}")
        
        # Test basic functions
        if hasattr(jmcomic, 'download_album'):
            print("✓ download_album function is available")
        else:
            print("✗ download_album function not found")
            
        if hasattr(jmcomic, 'create_option'):
            print("✓ create_option function is available")
        else:
            print("✗ create_option function not found")
            
        if hasattr(jmcomic, 'new_downloader'):
            print("✓ new_downloader function is available")
        else:
            print("✗ new_downloader function not found")
            
        return True
    except ImportError as e:
        print(f"✗ jmcomic library not available: {e}")
        return False

def test_task_download_import():
    """Test if our modified task_download module can be imported"""
    try:
        from task.task_download import TaskDownload, QtDownloadTask
        print("✓ TaskDownload module imported successfully")
        
        # Test if new methods exist
        task_download = TaskDownload()
        if hasattr(task_download, 'DownloadAlbum'):
            print("✓ DownloadAlbum method is available")
        else:
            print("✗ DownloadAlbum method not found")
            
        # Test QtDownloadTask modifications
        qt_task = QtDownloadTask()
        if hasattr(qt_task, 'isAlbumDownload'):
            print("✓ isAlbumDownload flag is available")
        else:
            print("✗ isAlbumDownload flag not found")
            
        if hasattr(qt_task, 'albumProgress'):
            print("✓ albumProgress field is available")
        else:
            print("✗ albumProgress field not found")
            
        return True
    except Exception as e:
        print(f"✗ TaskDownload module import failed: {e}")
        return False

def test_qt_task_base():
    """Test QtTaskBase modifications"""
    try:
        from task.qt_task import QtTaskBase
        print("✓ QtTaskBase imported successfully")
        
        # Create instance
        qt_task_base = QtTaskBase()
        if hasattr(qt_task_base, 'AddDownloadAlbum'):
            print("✓ AddDownloadAlbum method is available")
        else:
            print("✗ AddDownloadAlbum method not found")
            
        return True
    except Exception as e:
        print(f"✗ QtTaskBase test failed: {e}")
        return False

def test_download_status():
    """Test download status modifications"""
    try:
        from view.download.download_status import DownloadStatus
        print("✓ DownloadStatus imported successfully")
        
        # Note: We can't fully test without Qt environment
        print("  (Full testing requires Qt environment)")
        return True
    except Exception as e:
        print(f"✗ DownloadStatus test failed: {e}")
        return False

def test_download_item():
    """Test download item modifications"""
    try:
        from view.download.download_item import DownloadItem
        print("✓ DownloadItem imported successfully")
        
        # Test new method
        item = DownloadItem()
        if hasattr(item, 'GetAlbumSavePath'):
            print("✓ GetAlbumSavePath method is available")
            
            # Test the method
            item.bookId = "123456"
            item.title = "Test Album"
            path = item.GetAlbumSavePath()
            print(f"  Sample path: {path}")
        else:
            print("✗ GetAlbumSavePath method not found")
            
        return True
    except Exception as e:
        print(f"✗ DownloadItem test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing JMComic-qt Album Download Implementation")
    print("=" * 50)
    
    tests = [
        ("JMComic Library", test_jmcomic_import),
        ("TaskDownload Module", test_task_download_import),
        ("QtTaskBase", test_qt_task_base),
        ("DownloadStatus", test_download_status),
        ("DownloadItem", test_download_item),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Album download implementation is ready.")
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed. Please check the implementation.")

if __name__ == "__main__":
    main()
