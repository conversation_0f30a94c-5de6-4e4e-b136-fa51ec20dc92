name: release
on:
  push:
    tags:
      - '*'
jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      PACKAGE_PREFIX: ${{ steps.get-package_prefix.outputs.PACKAGE_PREFIX }}
      TAG_NAME: ${{ steps.get-package_prefix.outputs.TAG_NAME }}
      HEAD_SHA_SHORT: ${{ steps.get-package_prefix.outputs.HEAD_SHA_SHORT }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: '0'
      - name: get-package_prefix
        id: get-package_prefix
        run: |
          LIB_NAME=jmcomic
          TAG_NAME=$(git describe --abbrev=0 --tags)
          HEAD_SHA_SHORT=$(git rev-parse --short HEAD)
          echo "::set-output name=PACKAGE_PREFIX::${LIB_NAME}_${TAG_NAME}"
          echo "::set-output name=TAG_NAME::${TAG_NAME}"
          echo "::set-output name=HEAD_SHA_SHORT::${HEAD_SHA_SHORT}"

  release:
    needs: [setup]
    runs-on: ubuntu-latest
    outputs:
      Up_Url: ${{ steps.create_release.outputs.upload_url }}
    steps:
      - name: create_release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ needs.setup.outputs.TAG_NAME }}
          release_name: ${{ needs.setup.outputs.TAG_NAME }}
          draft: true
          prerelease: true

  macos:
    needs: [setup, release]
    runs-on: macos-latest
    env:
      PACKAGENAME: ${{ needs.setup.outputs.PACKAGE_PREFIX }}_macos_universal2
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller
        pip install https://github.com/tonquer/waifu2x-vulkan/releases/download/v1.1.6/sr_ncnn_vulkan-1.2.0-cp36.cp37.cp38.cp39.cp310.cp311.cp312.cp313-none-macosx_10_9_universal2.whl
        cd script
        pip install delocate wheel_filename
        python build_universal2.py pillow
        python build_universal2.py cffi
        python build_universal2.py curl_cffi
        python build_universal2.py pyyaml
        pip install *.whl
        cd ..
        pip install -r src/requirements_macos.txt
        brew install create-dmg
    - name: Build
      run: |
        cd src
        cp ../res/icon/Icon.icns ./
        pyinstaller --target-architecture=universal2 --clean --onedir --name JMComic \
            --hidden-import sr_ncnn_vulkan --hidden-import PySide6 --hidden-import requests \
            --hidden-import urllib3 --hidden-import websocket-client --hidden-import pillow \
            --hidden-import config \
            --hidden-import component \
            --hidden-import server \
            --hidden-import task \
            --hidden-import tools \
            --hidden-import view \
            --strip --windowed -i Icon.icns \
            start.py
        xattr -cr dist/JMComic.app
        create-dmg --volname "JMComic" --volicon "Icon.icns" --icon "JMComic.app" 200 190 \
            --window-pos 200 120 \
            --window-size 800 400 \
            --icon-size 100 \
            --hide-extension "JMComic.app" --app-drop-link 600 185 \
            ${{ env.PACKAGENAME }}.dmg dist/JMComic.app

        zip -9 jmcomic.zip ${{ env.PACKAGENAME }}.dmg
        mv ${{ env.PACKAGENAME }}.dmg ..
        mv jmcomic.zip ..
        cd ..
    - name: Upload
      uses: actions/upload-artifact@v4
      with:
        name: ${{ env.PACKAGENAME }}
        path: jmcomic.zip
    - name: upload-macos
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.release.outputs.Up_Url }}
        asset_path: ${{ env.PACKAGENAME }}.dmg
        asset_name: ${{ env.PACKAGENAME }}.dmg
        asset_content_type: application/gzip

  macos-nosr:
    needs: [setup, release]
    runs-on: macos-latest
    env:
      PACKAGENAME: ${{ needs.setup.outputs.PACKAGE_PREFIX }}_macos_nosr_universal2
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller
        # pip install https://github.com/tonquer/waifu2x-vulkan/releases/download/v1.1.6/sr_ncnn_vulkan-1.2.0-cp36.cp37.cp38.cp39.cp310.cp311.cp312.cp313-none-macosx_10_9_universal2.whl
        cd script
        pip install delocate wheel_filename
        python build_universal2.py pillow
        python build_universal2.py cffi
        python build_universal2.py curl_cffi
        python build_universal2.py pyyaml
        pip install *.whl
        cd ..
        pip install -r src/requirements_macos.txt
        brew install create-dmg
    - name: Build
      run: |
        cd src
        cp ../res/icon/Icon.icns ./
        pyinstaller --target-architecture=universal2 --clean --onedir --name JMComic \
            --hidden-import sr_ncnn_vulkan --hidden-import PySide6 --hidden-import requests \
            --hidden-import urllib3 --hidden-import websocket-client --hidden-import pillow \
            --hidden-import config \
            --hidden-import component \
            --hidden-import server \
            --hidden-import task \
            --hidden-import tools \
            --hidden-import view \
            --strip --windowed -i Icon.icns \
            start.py
        xattr -cr dist/JMComic.app
        create-dmg --volname "JMComic" --volicon "Icon.icns" --icon "JMComic.app" 200 190 \
            --window-pos 200 120 \
            --window-size 800 400 \
            --icon-size 100 \
            --hide-extension "JMComic.app" --app-drop-link 600 185 \
            ${{ env.PACKAGENAME }}.dmg dist/JMComic.app

        zip -9 jmcomic.zip ${{ env.PACKAGENAME }}.dmg
        mv ${{ env.PACKAGENAME }}.dmg ..
        mv jmcomic.zip ..
        cd ..
    - name: Upload
      uses: actions/upload-artifact@v4
      with:
        name: ${{ env.PACKAGENAME }}
        path: jmcomic.zip
    - name: upload-macos
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.release.outputs.Up_Url }}
        asset_path: ${{ env.PACKAGENAME }}.dmg
        asset_name: ${{ env.PACKAGENAME }}.dmg
        asset_content_type: application/gzip

  windows:
    needs: [setup, release]
    runs-on: windows-latest
    env:
      PACKAGENAME: ${{ needs.setup.outputs.PACKAGE_PREFIX }}_windows_x64
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.9
      uses: actions/setup-python@v5
      with:
        python-version: 3.9

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller
        pip install https://github.com/tonquer/waifu2x-vulkan/releases/download/v1.1.6/sr_ncnn_vulkan-1.2.0-cp36.cp37.cp38.cp39.cp310.cp311-none-win_amd64.whl
        pip install -r src\requirements.txt
    - name: Build
      run: |
        cd src
        cp ..\res\icon\icon.ico .\
        pyinstaller --hidden-import=_cffi_backend --collect-data curl_cffi --add-data "..\lib\win\*;." -F -w -i icon.ico start.py
        mv dist jmcomic
        cp ..\LICENSE jmcomic\
        cp ..\CHANGELOG jmcomic\
        cd ..
        mkdir ${{ env.PACKAGENAME }}
        mv src\jmcomic ${{ env.PACKAGENAME }}
        7z a -r "$($Env:PACKAGENAME + '.zip')" "jmcomic"
    - name: upload-macos
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.release.outputs.Up_Url }}
        asset_path: ${{ env.PACKAGENAME }}.zip
        asset_name: ${{ env.PACKAGENAME }}.zip
        asset_content_type: application/zip

  windows-nosr:
    needs: [setup, release]
    runs-on: windows-latest
    env:
      PACKAGENAME: ${{ needs.setup.outputs.PACKAGE_PREFIX }}_windows_nosr_x64
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.9
      uses: actions/setup-python@v5
      with:
        python-version: 3.9

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller
        # pip install https://github.com/tonquer/waifu2x-vulkan/releases/download/v1.1.6/sr_ncnn_vulkan-1.2.0-cp36.cp37.cp38.cp39.cp310.cp311-none-win_amd64.whl
        pip install -r src\requirements.txt
    - name: Build
      run: |
        cd src
        cp ..\res\icon\icon.ico .\
        pyinstaller --hidden-import=_cffi_backend --collect-data curl_cffi --add-data "..\lib\win\*;."  -F -w -i icon.ico start.py
        mv dist jmcomic
        cp ..\LICENSE jmcomic\
        cp ..\CHANGELOG jmcomic\
        cd ..
        mkdir ${{ env.PACKAGENAME }}
        mv src\jmcomic ${{ env.PACKAGENAME }}
        7z a -r "$($Env:PACKAGENAME + '.zip')" "jmcomic"
    - name: upload-macos
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.release.outputs.Up_Url }}
        asset_path: ${{ env.PACKAGENAME }}.zip
        asset_name: ${{ env.PACKAGENAME }}.zip
        asset_content_type: application/zip

  windows7:
    needs: [setup, release]
    runs-on: windows-latest
    env:
      PACKAGENAME: ${{ needs.setup.outputs.PACKAGE_PREFIX }}_windows7_x64
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.8
      uses: actions/setup-python@v5
      with:
        python-version: 3.8

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller==4.6
        pip install https://github.com/tonquer/waifu2x-vulkan/releases/download/v1.1.6/sr_ncnn_vulkan-1.2.0-cp36.cp37.cp38.cp39.cp310.cp311-none-win_amd64.whl
        pip install -r src\requirements_win7.txt
    - name: Build
      run: |
        cd src
        cp ..\res\icon\icon.ico .\
        pyinstaller --hidden-import=_cffi_backend --collect-data curl_cffi --add-data "..\lib\win\*;." -F -w -i icon.ico start.py
        mv dist jmcomic
        cp ..\LICENSE jmcomic\
        cp ..\CHANGELOG jmcomic\
        cd ..
        mkdir ${{ env.PACKAGENAME }}
        mv src\jmcomic ${{ env.PACKAGENAME }}
        7z a -r "$($Env:PACKAGENAME + '.zip')" "jmcomic"
    - name: upload-win
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.release.outputs.Up_Url }}
        asset_path: ${{ env.PACKAGENAME }}.zip
        asset_name: ${{ env.PACKAGENAME }}.zip
        asset_content_type: application/zip

  windows7-nosr:
    needs: [setup, release]
    runs-on: windows-latest
    env:
      PACKAGENAME: ${{ needs.setup.outputs.PACKAGE_PREFIX }}_windows7_nosr_x64
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.8
      uses: actions/setup-python@v5
      with:
        python-version: 3.8

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller==4.6
        # pip install https://github.com/tonquer/waifu2x-vulkan/releases/download/v1.1.6/sr_ncnn_vulkan-1.2.0-cp36.cp37.cp38.cp39.cp310.cp311-none-win_amd64.whl
        pip install -r src\requirements_win7.txt
    - name: Build
      run: |
        cd src
        cp ..\res\icon\icon.ico .\
        pyinstaller --hidden-import=_cffi_backend --collect-data curl_cffi --add-data "..\lib\win\*;."  -F -w -i icon.ico start.py
        mv dist jmcomic
        cp ..\LICENSE jmcomic\
        cp ..\CHANGELOG jmcomic\
        cd ..
        mkdir ${{ env.PACKAGENAME }}
        mv src\jmcomic ${{ env.PACKAGENAME }}
        7z a -r "$($Env:PACKAGENAME + '.zip')" "jmcomic"
    - name: upload-win
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.release.outputs.Up_Url }}
        asset_path: ${{ env.PACKAGENAME }}.zip
        asset_name: ${{ env.PACKAGENAME }}.zip
        asset_content_type: application/zip

  ubuntu:
    needs: [setup, release]
    runs-on: ubuntu-latest
    env:
      PACKAGENAME: ${{ needs.setup.outputs.PACKAGE_PREFIX }}_linux_x64
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.9
      uses: actions/setup-python@v5
      with:
        python-version: 3.9
    - name: Install dependencies
      run: |
        sudo apt-get install -y fuse libfuse2
        python -m pip install --upgrade pip
        pip install pyinstaller
        pip install https://github.com/tonquer/waifu2x-vulkan/releases/download/v1.1.6/sr_ncnn_vulkan-1.2.0-cp37-abi3-linux_x86_64.whl
        pip install -r src/requirements.txt
    - name: Build
      run: |
        cd src
        pyinstaller --hidden-import=_cffi_backend --collect-data curl_cffi --add-data "../lib/linux/*:." -w start.py
        cd dist
        mkdir -p jmcomic.AppRun/usr/bin
        wget https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage
        chmod +x appimagetool-x86_64.AppImage
        cp -r ../../res/appimage/* jmcomic.AppRun/
        cp -r ../../res/icon/logo_round.png jmcomic.AppRun/JMComic.png
        cp -r start/* jmcomic.AppRun/usr/bin/

        mv jmcomic.AppRun/usr/bin/start jmcomic.AppRun/usr/bin/JMComic
        chmod +x jmcomic.AppRun/AppRun
        chmod +x jmcomic.AppRun/usr/bin/JMComic
        ./appimagetool-x86_64.AppImage jmcomic.AppRun
        mv JMComic-x86_64.AppImage ../../${{ env.PACKAGENAME }}-x86_64.AppImage
        cd ../..
    - name: Upload
      uses: actions/upload-artifact@v4
      with:
        name: ${{ env.PACKAGENAME }}
        path: ${{ env.PACKAGENAME }}-x86_64.AppImage
    - name: upload-win
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.release.outputs.Up_Url }}
        asset_path: ${{ env.PACKAGENAME }}-x86_64.AppImage
        asset_name: ${{ env.PACKAGENAME }}-x86_64.AppImage
        asset_content_type: application/zip