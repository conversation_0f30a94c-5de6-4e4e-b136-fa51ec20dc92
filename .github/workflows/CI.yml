# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  windows:
    runs-on: windows-latest
    env:
      PACKAGENAME: jmcomic_py39_windows_x64
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.9
      uses: actions/setup-python@v5
      with:
        python-version: 3.9
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller
        pip install -r src\requirements.txt
        pip install https://github.com/tonquer/waifu2x-vulkan/releases/download/v1.1.6/sr_ncnn_vulkan-1.2.0-cp36.cp37.cp38.cp39.cp310.cp311-none-win_amd64.whl
    - name: Build
      run: |
        cd src
        cp ..\res\icon\icon.ico .\

        pyinstaller --hidden-import=_cffi_backend --collect-data curl_cffi --add-data "..\lib\win\*;." -F -w -i icon.ico start.py
        mv dist jmcomic
        cp ..\LICENSE jmcomic\
        cp ..\CHANGELOG jmcomic\
        cd ..
        mkdir ${{ env.PACKAGENAME }}
        mv src\jmcomic ${{ env.PACKAGENAME }}
        7z a -r "$($Env:PACKAGENAME + '.zip')" "jmcomic"
    - name: Upload
      uses: actions/upload-artifact@v4
      with:
        name: ${{ env.PACKAGENAME }}
        path: ${{ env.PACKAGENAME }}
        retention-days: 7
        
  windows7:
    runs-on: windows-latest
    env:
      PACKAGENAME: jmcomic_py38_windows7_x64
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.8
      uses: actions/setup-python@v5
      with:
        python-version: 3.8
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller==4.6
        pip install -r src\requirements_win7.txt
        pip install https://github.com/tonquer/waifu2x-vulkan/releases/download/v1.1.6/sr_ncnn_vulkan-1.2.0-cp36.cp37.cp38.cp39.cp310.cp311-none-win_amd64.whl
    - name: Build
      run: |
        cd src
        cp ..\res\icon\icon.ico .\
        pyinstaller --hidden-import=_cffi_backend --collect-data curl_cffi --add-data "..\lib\win\*;."  -F -w -i icon.ico start.py
        mv dist jmcomic
        cp ..\LICENSE jmcomic\
        cp ..\CHANGELOG jmcomic\
        cd ..
        mkdir ${{ env.PACKAGENAME }}
        mv src\jmcomic ${{ env.PACKAGENAME }}
        7z a -r "$($Env:PACKAGENAME + '.zip')" "jmcomic"
    - name: Upload
      uses: actions/upload-artifact@v4
      with:
        name: ${{ env.PACKAGENAME }}
        path: ${{ env.PACKAGENAME }}
        retention-days: 7
        
  windows7-nosr:
    runs-on: windows-latest
    env:
      PACKAGENAME: jmcomic_py38_windows7_nosr_x64
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.8
      uses: actions/setup-python@v5
      with:
        python-version: 3.8
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller==4.6
        pip install -r src\requirements_win7.txt
    - name: Build
      run: |
        cd src
        cp ..\res\icon\icon.ico .\
        pyinstaller --hidden-import=_cffi_backend --collect-data curl_cffi --add-data "..\lib\win\*;."  -F -w -i icon.ico start.py
        mv dist jmcomic
        cp ..\LICENSE jmcomic\
        cp ..\CHANGELOG jmcomic\
        cd ..
        mkdir ${{ env.PACKAGENAME }}
        mv src\jmcomic ${{ env.PACKAGENAME }}
        7z a -r "$($Env:PACKAGENAME + '.zip')" "jmcomic"
    - name: Upload
      uses: actions/upload-artifact@v4
      with:
        name: ${{ env.PACKAGENAME }}
        path: ${{ env.PACKAGENAME }}
        retention-days: 7

  ubuntu:
    runs-on: ubuntu-latest
    env:
      PACKAGENAME: jmcomic_py39_linux
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.9
      uses: actions/setup-python@v5
      with:
        python-version: 3.9
    - name: Install dependencies
      run: |
        sudo apt-get install -y fuse libfuse2
        python -m pip install --upgrade pip
        pip install pyinstaller
        pip install https://github.com/tonquer/waifu2x-vulkan/releases/download/v1.1.6/sr_ncnn_vulkan-1.2.0-cp37-abi3-linux_x86_64.whl
        pip install -r src/requirements.txt
    - name: Build
      run: |
        cd src
        pyinstaller --hidden-import=_cffi_backend --collect-data curl_cffi --add-data "../lib/linux/*:."  -w start.py
        cd dist
        mkdir -p jmcomic.AppRun/usr/bin
        wget https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage
        chmod +x appimagetool-x86_64.AppImage
        cp -r ../../res/appimage/* jmcomic.AppRun/
        cp -r ../../res/icon/logo_round.png jmcomic.AppRun/JMComic.png
        cp -r start/* jmcomic.AppRun/usr/bin/

        mv jmcomic.AppRun/usr/bin/start jmcomic.AppRun/usr/bin/JMComic
        chmod +x jmcomic.AppRun/AppRun
        chmod +x jmcomic.AppRun/usr/bin/JMComic
        ./appimagetool-x86_64.AppImage jmcomic.AppRun
        mv JMComic-x86_64.AppImage ../../${{ env.PACKAGENAME }}-x86_64.AppImage
        cd ../..
    - name: Upload
      uses: actions/upload-artifact@v4
      with:
        name: ${{ env.PACKAGENAME }}
        path: ${{ env.PACKAGENAME }}-x86_64.AppImage
        retention-days: 7

  macos:
    runs-on: macos-latest
    env:
      PACKAGENAME: jmcomic_py39_macos
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller
        pip install https://github.com/tonquer/waifu2x-vulkan/releases/download/v1.1.6/sr_ncnn_vulkan-1.2.0-cp36.cp37.cp38.cp39.cp310.cp311.cp312.cp313-none-macosx_10_9_universal2.whl
        cd script
        pip install delocate wheel_filename
        python build_universal2.py pillow
        python build_universal2.py cffi
        python build_universal2.py curl_cffi
        python build_universal2.py pyyaml
        pip install *.whl
        cd ..
        pip install -r src/requirements_macos.txt
        brew install create-dmg
    - name: Build
      run: |
        cd src
        cp ../res/icon/Icon.icns ./
        pyinstaller --target-architecture=universal2 --clean --onedir --name JMComic \
            --hidden-import sr_ncnn_vulkan --hidden-import PySide6 --hidden-import requests \
            --hidden-import urllib3 --hidden-import websocket-client --hidden-import pillow \
            --hidden-import config \
            --hidden-import component \
            --hidden-import server \
            --hidden-import task \
            --hidden-import tools \
            --hidden-import view \
            --strip --windowed -i Icon.icns \
            start.py
        xattr -cr dist/JMComic.app
        create-dmg --volname "JMComic" --volicon "Icon.icns" --icon "JMComic.app" 200 190 \
            --window-pos 200 120 \
            --window-size 800 400 \
            --icon-size 100 \
            --hide-extension "JMComic.app" --app-drop-link 600 185 \
            ${{ env.PACKAGENAME }}.dmg dist/JMComic.app

        zip -9 jmcomic.zip ${{ env.PACKAGENAME }}.dmg
        mv ${{ env.PACKAGENAME }}.dmg ..
        mv jmcomic.zip ..
        cd ..
    - name: Upload
      uses: actions/upload-artifact@v4
      with:
        name: ${{ env.PACKAGENAME }}
        path: ${{ env.PACKAGENAME }}.dmg
        retention-days: 7