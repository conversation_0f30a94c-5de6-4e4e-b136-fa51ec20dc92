<RCC>
  <qresource prefix="icon">
    <file>theme/light_pink/disabled/base.svg</file>
    <file>theme/light_pink/disabled/branch-closed.svg</file>
    <file>theme/light_pink/disabled/branch-end.svg</file>
    <file>theme/light_pink/disabled/branch-more.svg</file>
    <file>theme/light_pink/disabled/branch-open.svg</file>
    <file>theme/light_pink/disabled/checkbox_checked.svg</file>
    <file>theme/light_pink/disabled/checkbox_checked_invert.svg</file>
    <file>theme/light_pink/disabled/checkbox_indeterminate.svg</file>
    <file>theme/light_pink/disabled/checkbox_indeterminate_invert.svg</file>
    <file>theme/light_pink/disabled/checkbox_unchecked.svg</file>
    <file>theme/light_pink/disabled/checkbox_unchecked_invert.svg</file>
    <file>theme/light_pink/disabled/checklist.svg</file>
    <file>theme/light_pink/disabled/checklist_indeterminate.svg</file>
    <file>theme/light_pink/disabled/checklist_indeterminate_invert.svg</file>
    <file>theme/light_pink/disabled/checklist_invert.svg</file>
    <file>theme/light_pink/disabled/close.svg</file>
    <file>theme/light_pink/disabled/downarrow.svg</file>
    <file>theme/light_pink/disabled/downarrow2.svg</file>
    <file>theme/light_pink/disabled/float.svg</file>
    <file>theme/light_pink/disabled/leftarrow.svg</file>
    <file>theme/light_pink/disabled/leftarrow2.svg</file>
    <file>theme/light_pink/disabled/radiobutton_checked.svg</file>
    <file>theme/light_pink/disabled/radiobutton_checked_invert.svg</file>
    <file>theme/light_pink/disabled/radiobutton_unchecked.svg</file>
    <file>theme/light_pink/disabled/radiobutton_unchecked_invert.svg</file>
    <file>theme/light_pink/disabled/rightarrow.svg</file>
    <file>theme/light_pink/disabled/rightarrow2.svg</file>
    <file>theme/light_pink/disabled/sizegrip.svg</file>
    <file>theme/light_pink/disabled/slider.svg</file>
    <file>theme/light_pink/disabled/splitter-horizontal.svg</file>
    <file>theme/light_pink/disabled/splitter-vertical.svg</file>
    <file>theme/light_pink/disabled/tab_close.svg</file>
    <file>theme/light_pink/disabled/toolbar-handle-horizontal.svg</file>
    <file>theme/light_pink/disabled/toolbar-handle-vertical.svg</file>
    <file>theme/light_pink/disabled/uparrow.svg</file>
    <file>theme/light_pink/disabled/uparrow2.svg</file>
    <file>theme/light_pink/disabled/vline.svg</file>
    <file>theme/light_pink/primary/base.svg</file>
    <file>theme/light_pink/primary/branch-closed.svg</file>
    <file>theme/light_pink/primary/branch-end.svg</file>
    <file>theme/light_pink/primary/branch-more.svg</file>
    <file>theme/light_pink/primary/branch-open.svg</file>
    <file>theme/light_pink/primary/checkbox_checked.svg</file>
    <file>theme/light_pink/primary/checkbox_checked_invert.svg</file>
    <file>theme/light_pink/primary/checkbox_indeterminate.svg</file>
    <file>theme/light_pink/primary/checkbox_indeterminate_invert.svg</file>
    <file>theme/light_pink/primary/checkbox_unchecked.svg</file>
    <file>theme/light_pink/primary/checkbox_unchecked_invert.svg</file>
    <file>theme/light_pink/primary/checklist.svg</file>
    <file>theme/light_pink/primary/checklist_indeterminate.svg</file>
    <file>theme/light_pink/primary/checklist_indeterminate_invert.svg</file>
    <file>theme/light_pink/primary/checklist_invert.svg</file>
    <file>theme/light_pink/primary/close.svg</file>
    <file>theme/light_pink/primary/downarrow.svg</file>
    <file>theme/light_pink/primary/downarrow2.svg</file>
    <file>theme/light_pink/primary/float.svg</file>
    <file>theme/light_pink/primary/leftarrow.svg</file>
    <file>theme/light_pink/primary/leftarrow2.svg</file>
    <file>theme/light_pink/primary/radiobutton_checked.svg</file>
    <file>theme/light_pink/primary/radiobutton_checked_invert.svg</file>
    <file>theme/light_pink/primary/radiobutton_unchecked.svg</file>
    <file>theme/light_pink/primary/radiobutton_unchecked_invert.svg</file>
    <file>theme/light_pink/primary/rightarrow.svg</file>
    <file>theme/light_pink/primary/rightarrow2.svg</file>
    <file>theme/light_pink/primary/sizegrip.svg</file>
    <file>theme/light_pink/primary/slider.svg</file>
    <file>theme/light_pink/primary/splitter-horizontal.svg</file>
    <file>theme/light_pink/primary/splitter-vertical.svg</file>
    <file>theme/light_pink/primary/tab_close.svg</file>
    <file>theme/light_pink/primary/toolbar-handle-horizontal.svg</file>
    <file>theme/light_pink/primary/toolbar-handle-vertical.svg</file>
    <file>theme/light_pink/primary/uparrow.svg</file>
    <file>theme/light_pink/primary/uparrow2.svg</file>
    <file>theme/light_pink/primary/vline.svg</file>
  </qresource
  <qresource prefix="file">
    <file>light_pink.qss</file>
  </qresource
</RCC>
